---
alwaysApply: true
---

# 项目结构规则

## 项目概述
这是一个数据标注系统，专门用于教育领域的自适应学习服务。系统采用前后端分离架构，包含知识点管理、题目标注、质量检测、版本管理等核心功能。

## 目录结构
```
Annotation/
├── backend/           # FastAPI 后端服务
│   ├── app/          # 应用核心代码
│   │   ├── api/      # API 路由定义
│   │   ├── core/     # 核心配置和工具
│   │   ├── models/   # 数据库模型
│   │   ├── schemas/  # Pydantic 模式定义
│   │   └── services/ # 业务逻辑服务
│   ├── alembic/      # 数据库迁移
│   └── tests/        # 后端测试
├── frontend/         # React + TypeScript 前端
│   ├── src/
│   │   ├── components/ # 可复用组件
│   │   ├── pages/     # 页面组件
│   │   ├── services/  # API 服务
│   │   ├── stores/    # 状态管理
│   │   └── types/     # TypeScript 类型定义
│   └── public/        # 静态资源
├── docs/             # 项目文档
├── database/         # 数据库脚本和种子数据
└── docker/           # 容器化配置
```

## 核心模块
1. **用户管理模块** - 用户认证、权限控制
2. **知识点管理模块** - 知识本体构建、层级关系
3. **题目管理模块** - 题目录入、编辑、分类
4. **标注工作流模块** - 标注任务分配、审核流程
5. **质量检测模块** - 数据质量评估、异常检测
6. **版本管理模块** - 数据版本控制、回滚机制
7. **知识空间模块** - 知识状态建模、路径分析

## 技术栈
- **后端**: FastAPI + SQLAlchemy + PostgreSQL (Supabase)
- **前端**: React + TypeScript + Tailwind CSS + Vite
- **数据库**: PostgreSQL (Supabase) 
- **容器化**: Docker + Docker Compose
- **状态管理**: Zustand
- **UI组件**: shadcn/ui
- **图表可视化**: D3.js + Three.js

## 开发规范
- 使用 TypeScript 进行类型安全开发
- 遵循 RESTful API 设计原则
- 采用组件化开发模式
- 实施严格的代码审查流程
- 保持代码风格一致性

## 开发与测试
- 使用 ./scripts/start-dev.sh 在开发环境运行服务
- 使用 playwright mcp 调用浏览器进行集成测试