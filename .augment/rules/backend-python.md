---
alwaysApply: true
---

# 后端 Python 开发规则

## 框架和库
- 使用 FastAPI 作为 Web 框架
- SQLAlchemy 作为 ORM
- Pydantic 用于数据验证和序列化
- Alembic 用于数据库迁移
- pytest 用于单元测试

## 代码结构规范

### 模型定义 (models/)
- 所有模型继承自 [base.py](mdc:backend/app/models/base.py) 中的 `BaseModel`
- 使用 SQLAlchemy 声明式语法
- 模型类名使用 PascalCase，表名使用 snake_case
- 必须包含 `id`、`created_at`、`updated_at` 字段

```python
from app.models.base import BaseModel

class KnowledgePoint(BaseModel):
    __tablename__ = "knowledge_points"
    
    title = Column(String(255), nullable=False)
    description = Column(Text)
    path = Column(LTree)  # 层级路径
```

### API 路由 (api/)
- 路由按功能模块分组，放在 [endpoints/](mdc:backend/app/api/v1/endpoints/) 目录下
- 使用 FastAPI 的依赖注入系统
- 所有端点必须有适当的响应模型和状态码
- 使用 [deps.py](mdc:backend/app/api/deps.py) 中的依赖项

```python
from fastapi import APIRouter, Depends
from app.api.deps import get_current_user, get_db
from app.schemas.knowledge import KnowledgePointCreate, KnowledgePointResponse

router = APIRouter()

@router.post("/", response_model=KnowledgePointResponse)
async def create_knowledge_point(
    knowledge_point: KnowledgePointCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    pass
```

### 数据模式 (schemas/)
- 使用 Pydantic 模型定义输入输出数据结构
- 分别定义 Create、Update、Response 模式
- 使用适当的验证器和字段约束

```python
from pydantic import BaseModel, Field, validator
from typing import Optional, List
from datetime import datetime

class KnowledgePointBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None

class KnowledgePointCreate(KnowledgePointBase):
    parent_id: Optional[int] = None

class KnowledgePointResponse(KnowledgePointBase):
    id: int
    path: str
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True
```

### 服务层 (services/)
- 业务逻辑封装在服务类中
- 每个服务对应一个主要的业务实体
- 使用依赖注入获取数据库会话
- 处理业务异常和错误

```python
from sqlalchemy.orm import Session
from app.models.knowledge import KnowledgePoint
from app.schemas.knowledge import KnowledgePointCreate
from app.core.exceptions import BusinessException

class KnowledgeService:
    def __init__(self, db: Session):
        self.db = db
    
    def create_knowledge_point(self, data: KnowledgePointCreate) -> KnowledgePoint:
        # 业务逻辑实现
        pass
```

## 数据库操作规范

### 查询优化
- 使用 SQLAlchemy 的 `joinedload` 和 `selectinload` 避免 N+1 查询
- 为频繁查询的字段添加索引
- 使用分页查询处理大量数据

### 事务管理
- 使用上下文管理器确保事务正确提交或回滚
- 在服务层处理事务边界
- 避免长事务锁定资源

### 迁移脚本
- 使用 Alembic 管理数据库版本
- 迁移脚本必须是可逆的
- 在生产环境部署前测试迁移

## 错误处理
- 使用 [exceptions.py](mdc:backend/app/core/exceptions.py) 中定义的自定义异常
- 提供有意义的错误消息
- 记录错误日志用于调试

## 安全规范
- 使用 [security.py](mdc:backend/app/core/security.py) 中的安全工具
- 所有敏感操作需要身份验证
- 实施适当的权限检查
- 验证用户输入防止注入攻击

## 测试规范
- 为所有服务方法编写单元测试
- 使用 pytest fixtures 设置测试数据
- 模拟外部依赖
- 保持测试覆盖率在 80% 以上

## 性能优化
- 使用数据库连接池
- 实施查询缓存
- 异步处理长时间运行的任务
- 监控 API 响应时间
