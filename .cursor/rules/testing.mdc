---
alwaysApply: false
---
# 测试规范

## 测试策略
- 单元测试：测试独立的函数和方法
- 集成测试：测试组件间的交互
- 端到端测试：测试完整的用户流程
- 性能测试：测试系统性能和响应时间

## 后端测试 (Python)

### 测试框架
- 使用 pytest 作为测试框架
- pytest-asyncio 用于异步测试
- pytest-mock 用于模拟对象
- pytest-cov 用于覆盖率报告

### 测试结构
```python
# tests/test_knowledge_service.py
import pytest
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session

from app.services.knowledge_service import KnowledgeService
from app.models.knowledge import KnowledgePoint
from app.schemas.knowledge import KnowledgePointCreate

class TestKnowledgeService:
    @pytest.fixture
    def mock_db(self):
        return Mock(spec=Session)
    
    @pytest.fixture
    def knowledge_service(self, mock_db):
        return KnowledgeService(mock_db)
    
    def test_create_knowledge_point_success(self, knowledge_service, mock_db):
        # 测试数据
        data = KnowledgePointCreate(
            title="数学基础",
            description="基础数学知识点"
        )
        
        # 模拟数据库操作
        mock_db.add.return_value = None
        mock_db.commit.return_value = None
        mock_db.refresh.return_value = None
        
        # 执行测试
        result = knowledge_service.create_knowledge_point(data)
        
        # 断言
        assert result.title == "数学基础"
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
```

### 测试数据库
- 使用 SQLite 内存数据库进行测试
- 每个测试用例独立的数据库实例
- 使用 fixtures 设置测试数据

```python
# conftest.py
import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.models.base import Base

@pytest.fixture
def test_db():
    engine = create_engine("sqlite:///:memory:")
    Base.metadata.create_all(engine)
    SessionLocal = sessionmaker(bind=engine)
    session = SessionLocal()
    yield session
    session.close()
```

### API 测试
- 使用 TestClient 测试 FastAPI 端点
- 模拟认证和权限检查
- 测试各种状态码和响应格式

```python
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

def test_create_knowledge_point():
    response = client.post(
        "/api/v1/knowledge-points",
        json={"title": "数学基础", "description": "基础数学知识点"},
        headers={"Authorization": "Bearer test-token"}
    )
    assert response.status_code == 201
    assert response.json()["data"]["title"] == "数学基础"
```

## 前端测试 (TypeScript/React)

### 测试框架
- Vitest 作为测试运行器
- React Testing Library 用于组件测试
- Jest DOM 用于 DOM 断言
- MSW (Mock Service Worker) 用于 API 模拟

### 组件测试
```typescript
// components/users/user-form.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import { UserForm } from './user-form';

describe('UserForm', () => {
  const mockOnSubmit = vi.fn();

  beforeEach(() => {
    mockOnSubmit.mockClear();
  });

  it('应该渲染表单字段', () => {
    render(<UserForm onSubmit={mockOnSubmit} />);
    
    expect(screen.getByLabelText('用户名')).toBeInTheDocument();
    expect(screen.getByLabelText('邮箱')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: '提交' })).toBeInTheDocument();
  });

  it('应该验证必填字段', async () => {
    render(<UserForm onSubmit={mockOnSubmit} />);
    
    fireEvent.click(screen.getByRole('button', { name: '提交' }));
    
    await waitFor(() => {
      expect(screen.getByText('用户名不能为空')).toBeInTheDocument();
    });
  });

  it('应该提交有效数据', async () => {
    render(<UserForm onSubmit={mockOnSubmit} />);
    
    fireEvent.change(screen.getByLabelText('用户名'), {
      target: { value: 'testuser' }
    });
    fireEvent.change(screen.getByLabelText('邮箱'), {
      target: { value: '<EMAIL>' }
    });
    
    fireEvent.click(screen.getByRole('button', { name: '提交' }));
    
    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        username: 'testuser',
        email: '<EMAIL>'
      });
    });
  });
});
```

### Hook 测试
```typescript
// hooks/use-knowledge-points.test.ts
import { renderHook, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import { useKnowledgePoints } from './use-knowledge-points';

// 模拟 API 服务
vi.mock('@/services/knowledge-points', () => ({
  knowledgePointService: {
    getKnowledgePoints: vi.fn()
  }
}));

describe('useKnowledgePoints', () => {
  it('应该获取知识点列表', async () => {
    const mockData = [
      { id: 1, title: '数学基础' },
      { id: 2, title: '物理基础' }
    ];
    
    knowledgePointService.getKnowledgePoints.mockResolvedValue(mockData);
    
    const { result } = renderHook(() => useKnowledgePoints());
    
    await waitFor(() => {
      expect(result.current.data).toEqual(mockData);
      expect(result.current.loading).toBe(false);
    });
  });
});
```

### API 模拟
```typescript
// test/mocks/handlers.ts
import { rest } from 'msw';

export const handlers = [
  rest.get('/api/v1/knowledge-points', (req, res, ctx) => {
    return res(
      ctx.json({
        success: true,
        data: {
          items: [
            { id: 1, title: '数学基础' },
            { id: 2, title: '物理基础' }
          ]
        }
      })
    );
  }),
  
  rest.post('/api/v1/knowledge-points', (req, res, ctx) => {
    return res(
      ctx.status(201),
      ctx.json({
        success: true,
        data: { id: 3, title: '新知识点' }
      })
    );
  })
];
```

## 测试配置

### 后端测试配置
```python
# pytest.ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --cov=app
    --cov-report=html
    --cov-report=term-missing
    --cov-fail-under=80
```

### 前端测试配置
```typescript
// vitest.config.ts
import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  test: {
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'html'],
      threshold: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    }
  }
});
```

## 测试最佳实践

### 通用原则
- 测试应该独立且可重复
- 使用描述性的测试名称
- 遵循 AAA 模式：Arrange, Act, Assert
- 每个测试只验证一个行为

### 测试数据
- 使用 Factory 模式创建测试数据
- 避免硬编码测试数据
- 使用有意义的测试数据

### 模拟和存根
- 模拟外部依赖
- 避免测试实际的外部服务
- 使用合适的模拟策略

### 断言
- 使用具体的断言
- 验证重要的业务逻辑
- 检查边界条件和错误情况

## 持续集成测试

### 测试流水线
```yaml
# .github/workflows/test.yml
name: Test
on: [push, pull_request]

jobs:
  backend-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      - name: Install dependencies
        run: |
          cd backend
          pip install -r requirements.txt
      - name: Run tests
        run: |
          cd backend
          pytest --cov=app --cov-report=xml
      - name: Upload coverage
        uses: codecov/codecov-action@v3

  frontend-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Install dependencies
        run: |
          cd frontend
          npm ci
      - name: Run tests
        run: |
          cd frontend
          npm run test:coverage
```

### 测试覆盖率
- 保持测试覆盖率在 80% 以上
- 重点关注业务逻辑的覆盖
- 定期审查测试覆盖率报告

### 性能测试
- 使用 Locust 进行后端性能测试
- 使用 Lighthouse 进行前端性能测试
- 监控关键指标和响应时间
