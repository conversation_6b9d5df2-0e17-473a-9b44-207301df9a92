---
alwaysApply: true
---

# 安全开发规范

## 认证和授权

### JWT Token 安全
- 使用强随机密钥生成 JWT
- 设置合适的 Token 过期时间
- 实施 Token 刷新机制
- 在服务端维护 Token 黑名单

```python
# 后端 JWT 配置
from datetime import datetime, timedelta
from jose import JWTError, jwt

SECRET_KEY = "your-secret-key-here"  # 应使用环境变量
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt
```

### 权限控制
- 实施基于角色的访问控制 (RBAC)
- 最小权限原则
- 资源级别的权限检查
- 定期审查用户权限

```python
# 权限装饰器示例
from functools import wraps
from fastapi import HTTPException, status

def require_permission(permission: str):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            current_user = kwargs.get('current_user')
            if not current_user or not current_user.has_permission(permission):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="权限不足"
                )
            return await func(*args, **kwargs)
        return wrapper
    return decorator
```

## 输入验证和数据清理

### 后端验证
- 使用 Pydantic 进行数据验证
- 验证所有用户输入
- 实施严格的数据类型检查
- 防止 SQL 注入和 XSS 攻击

```python
from pydantic import BaseModel, Field, validator
from typing import Optional
import re

class UserCreate(BaseModel):
    username: str = Field(..., min_length=3, max_length=50)
    email: str = Field(..., regex=r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
    password: str = Field(..., min_length=8)
    
    @validator('username')
    def validate_username(cls, v):
        if not re.match(r'^[a-zA-Z0-9_]+$', v):
            raise ValueError('用户名只能包含字母、数字和下划线')
        return v
    
    @validator('password')
    def validate_password(cls, v):
        if not re.search(r'[A-Z]', v):
            raise ValueError('密码必须包含至少一个大写字母')
        if not re.search(r'[a-z]', v):
            raise ValueError('密码必须包含至少一个小写字母')
        if not re.search(r'\d', v):
            raise ValueError('密码必须包含至少一个数字')
        return v
```

### 前端验证
- 使用 Zod 进行客户端验证
- 不依赖客户端验证作为唯一安全措施
- 实施 XSS 防护

```typescript
import { z } from 'zod';

const userSchema = z.object({
  username: z.string()
    .min(3, '用户名至少3个字符')
    .max(50, '用户名最多50个字符')
    .regex(/^[a-zA-Z0-9_]+$/, '用户名只能包含字母、数字和下划线'),
  
  email: z.string()
    .email('邮箱格式不正确'),
  
  password: z.string()
    .min(8, '密码至少8个字符')
    .regex(/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, '密码必须包含大小写字母和数字')
});
```

## 密码安全

### 密码哈希
- 使用 bcrypt 或 Argon2 进行密码哈希
- 使用适当的盐值和迭代次数
- 永不存储明文密码

```python
from passlib.context import CryptContext

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def hash_password(password: str) -> str:
    return pwd_context.hash(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)
```

### 密码策略
- 强制使用强密码
- 实施密码历史记录
- 定期提醒用户更改密码
- 支持双因素认证

## 数据库安全

### SQL 注入防护
- 使用参数化查询
- 避免动态 SQL 构建
- 使用 ORM 的安全特性

```python
# 安全的查询示例
def get_user_by_id(db: Session, user_id: int):
    return db.query(User).filter(User.id == user_id).first()

# 危险的查询示例 - 避免使用
def get_user_by_id_unsafe(db: Session, user_id: str):
    query = f"SELECT * FROM users WHERE id = {user_id}"  # 危险！
    return db.execute(query)
```

### 数据库访问控制
- 使用最小权限的数据库用户
- 限制数据库连接来源
- 定期审查数据库权限
- 实施数据加密

## API 安全

### 请求限制
- 实施 API 限流
- 防止暴力破解攻击
- 监控异常请求模式

```python
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded

limiter = Limiter(key_func=get_remote_address)

@app.post("/api/v1/auth/login")
@limiter.limit("5/minute")
async def login(request: Request, user_credentials: UserLogin):
    # 登录逻辑
    pass
```

### CORS 配置
- 严格配置 CORS 策略
- 只允许信任的域名
- 避免使用通配符

```python
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://yourdomain.com"],  # 不要使用 "*"
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)
```

## 前端安全

### XSS 防护
- 对用户输入进行 HTML 转义
- 使用内容安全策略 (CSP)
- 避免使用 `dangerouslySetInnerHTML`

```typescript
// 安全的内容渲染
const SafeContent: React.FC<{ content: string }> = ({ content }) => {
  return <div>{content}</div>; // React 自动转义
};

// 危险的内容渲染 - 避免使用
const UnsafeContent: React.FC<{ content: string }> = ({ content }) => {
  return <div dangerouslySetInnerHTML={{ __html: content }} />; // 危险！
};
```

### 敏感数据处理
- 不在客户端存储敏感数据
- 使用 HTTPS 传输数据
- 实施适当的会话管理

```typescript
// 安全的 Token 存储
const storeToken = (token: string) => {
  // 使用 httpOnly cookie 或安全的存储方式
  document.cookie = `token=${token}; secure; httpOnly; samesite=strict`;
};

// 不安全的存储方式 - 避免使用
const unsafeStoreToken = (token: string) => {
  localStorage.setItem('token', token); // 容易受到 XSS 攻击
};
```

## 文件上传安全

### 文件类型验证
- 验证文件扩展名和 MIME 类型
- 限制文件大小
- 扫描恶意文件

```python
from fastapi import UploadFile, HTTPException
import magic

ALLOWED_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.gif', '.pdf'}
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB

async def validate_file(file: UploadFile):
    # 检查文件扩展名
    file_ext = os.path.splitext(file.filename)[1].lower()
    if file_ext not in ALLOWED_EXTENSIONS:
        raise HTTPException(status_code=400, detail="不支持的文件类型")
    
    # 检查文件大小
    file_content = await file.read()
    if len(file_content) > MAX_FILE_SIZE:
        raise HTTPException(status_code=400, detail="文件过大")
    
    # 检查 MIME 类型
    mime_type = magic.from_buffer(file_content, mime=True)
    if not mime_type.startswith(('image/', 'application/pdf')):
        raise HTTPException(status_code=400, detail="无效的文件类型")
    
    return file_content
```

## 日志和监控

### 安全日志
- 记录所有认证和授权事件
- 记录敏感操作
- 不记录敏感数据
- 实施日志轮转和备份

```python
import logging
from datetime import datetime

security_logger = logging.getLogger('security')

def log_login_attempt(username: str, ip_address: str, success: bool):
    security_logger.info(
        f"Login attempt - Username: {username}, IP: {ip_address}, "
        f"Success: {success}, Time: {datetime.utcnow()}"
    )

def log_permission_denied(user_id: int, resource: str, action: str):
    security_logger.warning(
        f"Permission denied - User: {user_id}, Resource: {resource}, "
        f"Action: {action}, Time: {datetime.utcnow()}"
    )
```

### 异常监控
- 监控异常登录模式
- 检测 API 滥用
- 实施自动报警机制

## 部署安全

### 环境变量
- 使用环境变量存储敏感配置
- 不在代码中硬编码密钥
- 使用密钥管理服务

```python
import os
from pydantic import BaseSettings

class Settings(BaseSettings):
    database_url: str
    secret_key: str
    jwt_secret: str
    
    class Config:
        env_file = ".env"

settings = Settings()
```

### HTTPS 配置
- 强制使用 HTTPS
- 配置安全的 TLS 设置
- 实施 HSTS 头部

```python
from fastapi.middleware.httpsredirect import HTTPSRedirectMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware

# 强制 HTTPS
app.add_middleware(HTTPSRedirectMiddleware)

# 限制可信主机
app.add_middleware(
    TrustedHostMiddleware, 
    allowed_hosts=["yourdomain.com", "*.yourdomain.com"]
)
```

## 安全测试

### 安全扫描
- 定期进行安全扫描
- 使用自动化安全测试工具
- 进行渗透测试

### 依赖项安全
- 定期更新依赖项
- 使用安全扫描工具检查依赖项
- 监控安全漏洞公告

```bash
# Python 依赖安全检查
pip install safety
safety check

# Node.js 依赖安全检查
npm audit
npm audit fix
```

## 事件响应

### 安全事件处理
- 制定安全事件响应计划
- 实施快速响应机制
- 定期进行安全演练

### 数据泄露响应
- 立即隔离受影响系统
- 评估泄露范围
- 通知相关用户和监管机构
- 实施补救措施
