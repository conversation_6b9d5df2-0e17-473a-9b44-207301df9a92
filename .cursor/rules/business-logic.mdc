---
alwaysApply: true
---

# 数据标注系统业务逻辑规则

## 知识点管理业务规则

### 知识点层级结构
- 知识点采用树形结构组织
- 使用 LTREE 数据类型存储路径信息
- 路径格式：`parent1.parent2.current`

```python
# 知识点路径验证
def validate_knowledge_point_path(path: str, max_depth: int = 10) -> bool:
    """验证知识点路径的有效性"""
    if not path:
        return False
    
    levels = path.split('.')
    if len(levels) > max_depth:
        raise ValueError(f"知识点层级不能超过 {max_depth} 层")
    
    # 验证每个层级的命名规范
    for level in levels:
        if not re.match(r'^[a-zA-Z0-9_\u4e00-\u9fff]+$', level):
            raise ValueError("知识点路径只能包含字母、数字、下划线和中文")
    
    return True
```

### 前置关系约束
- 知识点之间可以建立前置关系
- 前置关系不能形成循环依赖
- 一个知识点可以有多个前置条件
- 前置关系具有传递性

```python
def check_prerequisite_cycle(knowledge_point_id: int, prerequisite_id: int, db: Session) -> bool:
    """检查前置关系是否会形成循环依赖"""
    
    def has_path(start_id: int, end_id: int, visited: set) -> bool:
        if start_id == end_id:
            return True
        if start_id in visited:
            return False
        
        visited.add(start_id)
        prerequisites = db.query(PrerequisiteRelation).filter(
            PrerequisiteRelation.knowledge_point_id == start_id
        ).all()
        
        for prereq in prerequisites:
            if has_path(prereq.prerequisite_id, end_id, visited):
                return True
        
        return False
    
    # 检查添加新前置关系后是否会形成循环
    return has_path(prerequisite_id, knowledge_point_id, set())
```

## 题目管理业务规则

### 题目类型约束
- 支持的题目类型：SC(单选)、MC(多选)、TF(判断)、Cloze(填空)、Essay(问答)
- 每种题型有特定的数据结构要求
- 题目内容必须包含题干和选项（除问答题外）

```python
from enum import Enum

class QuestionType(str, Enum):
    SINGLE_CHOICE = "SC"
    MULTIPLE_CHOICE = "MC"
    TRUE_FALSE = "TF"
    CLOZE = "Cloze"
    ESSAY = "Essay"

def validate_question_content(question_type: QuestionType, content: dict) -> bool:
    """验证题目内容的完整性"""
    required_fields = ["stem"]  # 题干是必需的
    
    if question_type in [QuestionType.SINGLE_CHOICE, QuestionType.MULTIPLE_CHOICE]:
        required_fields.extend(["options", "correct_answer"])
        if "options" in content and len(content["options"]) < 2:
            raise ValueError("选择题至少需要2个选项")
    
    elif question_type == QuestionType.TRUE_FALSE:
        required_fields.append("correct_answer")
        if "correct_answer" in content and content["correct_answer"] not in [True, False]:
            raise ValueError("判断题答案必须是布尔值")
    
    elif question_type == QuestionType.CLOZE:
        required_fields.append("blanks")
        if "blanks" in content and not content["blanks"]:
            raise ValueError("填空题必须包含空白位置信息")
    
    # 检查必需字段
    for field in required_fields:
        if field not in content or not content[field]:
            raise ValueError(f"题目缺少必需字段: {field}")
    
    return True
```

### 题目-知识点映射规则
- 每个题目至少关联一个知识点
- 题目可以关联多个知识点（Q-矩阵）
- 映射关系包含权重信息（0-1之间）
- 权重总和建议不超过题目总分

```python
def validate_question_knowledge_mapping(question_id: int, mappings: List[dict], db: Session) -> bool:
    """验证题目-知识点映射的有效性"""
    if not mappings:
        raise ValueError("题目必须至少关联一个知识点")
    
    total_weight = 0
    knowledge_point_ids = set()
    
    for mapping in mappings:
        kp_id = mapping.get("knowledge_point_id")
        weight = mapping.get("weight", 1.0)
        
        # 检查知识点是否存在
        kp = db.query(KnowledgePoint).filter(KnowledgePoint.id == kp_id).first()
        if not kp:
            raise ValueError(f"知识点 {kp_id} 不存在")
        
        # 检查权重范围
        if not 0 < weight <= 1:
            raise ValueError("权重必须在 0 到 1 之间")
        
        # 检查重复关联
        if kp_id in knowledge_point_ids:
            raise ValueError(f"不能重复关联知识点 {kp_id}")
        
        knowledge_point_ids.add(kp_id)
        total_weight += weight
    
    # 权重总和建议不超过1
    if total_weight > 1.1:  # 允许小幅超出
        raise ValueError("权重总和不应超过 1.0")
    
    return True
```

## 标注工作流业务规则

### 任务分配规则
- 每个标注任务分配给特定的标注员
- 标注员只能处理分配给自己的任务
- 任务状态流转：pending -> in_progress -> review -> done
- 审核员不能审核自己标注的任务

```python
from enum import Enum

class TaskStatus(str, Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    REVIEW = "review"
    DONE = "done"
    REJECTED = "rejected"

def validate_task_assignment(task_id: int, assignee_id: int, assigner_id: int, db: Session) -> bool:
    """验证任务分配的有效性"""
    # 检查分配者权限
    assigner = db.query(User).filter(User.id == assigner_id).first()
    if not assigner or assigner.role not in ["admin", "reviewer"]:
        raise ValueError("只有管理员或审核员可以分配任务")
    
    # 检查被分配者角色
    assignee = db.query(User).filter(User.id == assignee_id).first()
    if not assignee or assignee.role not in ["annotator", "reviewer"]:
        raise ValueError("只能分配给标注员或审核员")
    
    # 检查任务状态
    task = db.query(AnnotationTask).filter(AnnotationTask.id == task_id).first()
    if task.status != TaskStatus.PENDING:
        raise ValueError("只能分配待处理的任务")
    
    return True

def validate_status_transition(current_status: TaskStatus, new_status: TaskStatus, user_role: str) -> bool:
    """验证任务状态转换的有效性"""
    valid_transitions = {
        TaskStatus.PENDING: [TaskStatus.IN_PROGRESS],
        TaskStatus.IN_PROGRESS: [TaskStatus.REVIEW, TaskStatus.PENDING],
        TaskStatus.REVIEW: [TaskStatus.DONE, TaskStatus.REJECTED],
        TaskStatus.REJECTED: [TaskStatus.IN_PROGRESS],
        TaskStatus.DONE: []  # 完成状态不能转换
    }
    
    if new_status not in valid_transitions.get(current_status, []):
        raise ValueError(f"不能从 {current_status} 转换到 {new_status}")
    
    # 检查角色权限
    if new_status == TaskStatus.DONE and user_role not in ["admin", "reviewer"]:
        raise ValueError("只有管理员或审核员可以完成任务")
    
    return True
```

### 标注质量控制
- 标注结果需要包含必要的元数据
- 实施多轮标注和交叉验证
- 记录标注时间和修改历史

```python
def validate_annotation_result(result: dict, question_type: QuestionType) -> bool:
    """验证标注结果的完整性"""
    required_fields = ["knowledge_points", "difficulty", "annotator_id", "annotation_time"]
    
    for field in required_fields:
        if field not in result:
            raise ValueError(f"标注结果缺少必需字段: {field}")
    
    # 验证知识点标注
    if not result["knowledge_points"]:
        raise ValueError("必须标注至少一个知识点")
    
    # 验证难度等级
    if result["difficulty"] not in ["easy", "medium", "hard"]:
        raise ValueError("难度等级必须是 easy、medium 或 hard")
    
    # 验证题型特定的标注
    if question_type == QuestionType.SINGLE_CHOICE:
        if "distractor_analysis" not in result:
            raise ValueError("单选题需要干扰项分析")
    
    return True
```

## 质量检测业务规则

### 异常检测规则
- 检测标注一致性异常
- 检测标注时间异常
- 检测知识点分布异常
- 检测难度分布异常

```python
def detect_annotation_anomalies(user_id: int, time_window: int, db: Session) -> List[dict]:
    """检测用户标注异常"""
    anomalies = []
    
    # 获取用户在时间窗口内的标注记录
    recent_annotations = db.query(AnnotationLog).filter(
        AnnotationLog.user_id == user_id,
        AnnotationLog.created_at >= datetime.utcnow() - timedelta(days=time_window)
    ).all()
    
    if not recent_annotations:
        return anomalies
    
    # 检测标注速度异常
    annotation_times = [log.annotation_time for log in recent_annotations if log.annotation_time]
    if annotation_times:
        avg_time = sum(annotation_times) / len(annotation_times)
        for log in recent_annotations:
            if log.annotation_time and log.annotation_time < avg_time * 0.3:
                anomalies.append({
                    "type": "fast_annotation",
                    "log_id": log.id,
                    "message": f"标注时间异常快: {log.annotation_time}秒"
                })
    
    # 检测一致性异常
    knowledge_point_distribution = {}
    for log in recent_annotations:
        for kp in log.knowledge_points:
            knowledge_point_distribution[kp] = knowledge_point_distribution.get(kp, 0) + 1
    
    # 如果某个知识点标注频率过高，可能存在问题
    total_annotations = len(recent_annotations)
    for kp, count in knowledge_point_distribution.items():
        if count / total_annotations > 0.8:  # 80%以上都标注同一个知识点
            anomalies.append({
                "type": "consistency_anomaly",
                "knowledge_point": kp,
                "message": f"知识点 {kp} 标注频率异常高: {count}/{total_annotations}"
            })
    
    return anomalies
```

## 版本管理业务规则

### 版本创建规则
- 重要数据变更需要创建版本快照
- 版本创建需要记录变更原因
- 版本标签必须唯一
- 支持版本回滚操作

```python
def create_version_snapshot(entity_type: str, entity_id: int, reason: str, user_id: int, db: Session) -> Version:
    """创建版本快照"""
    # 验证实体类型
    if entity_type not in ["question", "knowledge_point", "annotation_task"]:
        raise ValueError("不支持的实体类型")
    
    # 获取当前实体数据
    if entity_type == "question":
        entity = db.query(Question).filter(Question.id == entity_id).first()
    elif entity_type == "knowledge_point":
        entity = db.query(KnowledgePoint).filter(KnowledgePoint.id == entity_id).first()
    else:
        entity = db.query(AnnotationTask).filter(AnnotationTask.id == entity_id).first()
    
    if not entity:
        raise ValueError(f"{entity_type} {entity_id} 不存在")
    
    # 创建版本快照
    version = Version(
        entity_type=entity_type,
        entity_id=entity_id,
        version_tag=f"{entity_type}_{entity_id}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
        data_snapshot=entity.to_dict(),
        change_reason=reason,
        created_by=user_id
    )
    
    db.add(version)
    db.commit()
    
    return version
```

## 数据导入导出业务规则

### 导入验证规则
- 导入数据必须符合格式要求
- 批量导入需要事务支持
- 导入失败需要回滚
- 记录导入日志

```python
def validate_import_data(data: List[dict], data_type: str) -> List[dict]:
    """验证导入数据的格式和内容"""
    errors = []
    
    for i, item in enumerate(data):
        try:
            if data_type == "question":
                validate_question_import_item(item)
            elif data_type == "knowledge_point":
                validate_knowledge_point_import_item(item)
            else:
                raise ValueError(f"不支持的数据类型: {data_type}")
                
        except ValueError as e:
            errors.append({
                "row": i + 1,
                "error": str(e),
                "data": item
            })
    
    return errors

def validate_question_import_item(item: dict) -> bool:
    """验证单个题目导入项"""
    required_fields = ["title", "type", "content", "knowledge_points"]
    
    for field in required_fields:
        if field not in item or not item[field]:
            raise ValueError(f"缺少必需字段: {field}")
    
    # 验证题目类型
    if item["type"] not in [t.value for t in QuestionType]:
        raise ValueError(f"无效的题目类型: {item['type']}")
    
    # 验证内容格式
    validate_question_content(QuestionType(item["type"]), item["content"])
    
    return True
```

## 权限控制业务规则

### 角色权限矩阵
- admin: 所有操作权限
- reviewer: 审核、查看权限
- annotator: 标注、查看权限
- viewer: 只读权限
- engine: 系统级操作权限

```python
ROLE_PERMISSIONS = {
    "admin": [
        "user:create", "user:read", "user:update", "user:delete",
        "question:create", "question:read", "question:update", "question:delete",
        "knowledge_point:create", "knowledge_point:read", "knowledge_point:update", "knowledge_point:delete",
        "annotation:create", "annotation:read", "annotation:update", "annotation:delete",
        "task:create", "task:read", "task:update", "task:delete", "task:assign",
        "quality:read", "quality:report",
        "version:create", "version:read", "version:restore"
    ],
    "reviewer": [
        "question:read", "question:update",
        "knowledge_point:read",
        "annotation:read", "annotation:update", "annotation:review",
        "task:read", "task:update", "task:assign",
        "quality:read", "quality:report",
        "version:read"
    ],
    "annotator": [
        "question:read",
        "knowledge_point:read",
        "annotation:create", "annotation:read", "annotation:update",
        "task:read", "task:update"
    ],
    "viewer": [
        "question:read",
        "knowledge_point:read",
        "annotation:read",
        "task:read",
        "quality:read"
    ],
    "engine": [
        "question:read", "question:update",
        "knowledge_point:read",
        "annotation:read", "annotation:update",
        "task:read", "task:update",
        "quality:read", "quality:analyze"
    ]
}

def check_permission(user_role: str, permission: str) -> bool:
    """检查用户角色是否具有特定权限"""
    return permission in ROLE_PERMISSIONS.get(user_role, [])
```

## 业务指标计算规则

### 质量指标计算
- 标注一致性率
- 标注完成率
- 平均标注时间
- 错误率统计

```python
def calculate_annotation_metrics(user_id: int, time_period: int, db: Session) -> dict:
    """计算标注员的业务指标"""
    start_date = datetime.utcnow() - timedelta(days=time_period)
    
    # 获取用户标注记录
    annotations = db.query(AnnotationLog).filter(
        AnnotationLog.user_id == user_id,
        AnnotationLog.created_at >= start_date
    ).all()
    
    if not annotations:
        return {"message": "暂无标注数据"}
    
    # 计算完成率
    total_assigned = db.query(AnnotationTask).filter(
        AnnotationTask.assignee_id == user_id,
        AnnotationTask.created_at >= start_date
    ).count()
    
    completed_tasks = db.query(AnnotationTask).filter(
        AnnotationTask.assignee_id == user_id,
        AnnotationTask.status == TaskStatus.DONE,
        AnnotationTask.created_at >= start_date
    ).count()
    
    completion_rate = completed_tasks / total_assigned if total_assigned > 0 else 0
    
    # 计算平均标注时间
    annotation_times = [log.annotation_time for log in annotations if log.annotation_time]
    avg_annotation_time = sum(annotation_times) / len(annotation_times) if annotation_times else 0
    
    # 计算一致性率（需要与其他标注员的结果对比）
    consistency_rate = calculate_consistency_rate(user_id, time_period, db)
    
    return {
        "total_annotations": len(annotations),
        "completion_rate": completion_rate,
        "avg_annotation_time": avg_annotation_time,
        "consistency_rate": consistency_rate
    }
```
