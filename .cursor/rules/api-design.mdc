---
alwaysApply: true
---

# API 设计规范

## RESTful API 设计原则

### URL 设计
- 使用名词而非动词
- 使用复数形式表示资源集合
- 使用层级结构表示资源关系
- 使用小写字母和连字符

```
✅ 正确示例:
GET /api/v1/users
GET /api/v1/users/123
GET /api/v1/users/123/knowledge-points
POST /api/v1/annotation-tasks

❌ 错误示例:
GET /api/v1/getUsers
GET /api/v1/user/123
GET /api/v1/users/123/getKnowledgePoints
```

### HTTP 方法使用
- `GET` - 获取资源
- `POST` - 创建资源
- `PUT` - 完整更新资源
- `PATCH` - 部分更新资源
- `DELETE` - 删除资源

### 状态码规范
- `200 OK` - 成功获取资源
- `201 Created` - 成功创建资源
- `204 No Content` - 成功删除资源
- `400 Bad Request` - 请求参数错误
- `401 Unauthorized` - 未认证
- `403 Forbidden` - 无权限
- `404 Not Found` - 资源不存在
- `422 Unprocessable Entity` - 验证失败
- `500 Internal Server Error` - 服务器错误

## API 版本管理
- 使用 URL 路径版本控制 (`/api/v1/`)
- 向后兼容原则
- 废弃版本提前通知

## 请求和响应格式

### 请求格式
```json
{
  "title": "数学基础",
  "description": "基础数学知识点",
  "parent_id": 1,
  "metadata": {
    "difficulty": "easy",
    "tags": ["math", "basic"]
  }
}
```

### 成功响应格式
```json
{
  "success": true,
  "data": {
    "id": 123,
    "title": "数学基础",
    "description": "基础数学知识点",
    "parent_id": 1,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  },
  "message": "操作成功"
}
```

### 错误响应格式
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "输入数据验证失败",
    "details": [
      {
        "field": "title",
        "message": "标题不能为空"
      }
    ]
  }
}
```

### 列表响应格式
```json
{
  "success": true,
  "data": {
    "items": [...],
    "pagination": {
      "page": 1,
      "per_page": 20,
      "total": 100,
      "total_pages": 5
    }
  }
}
```

## 业务模块 API 设计

### 用户管理 API
```
POST /api/v1/auth/login          # 用户登录
POST /api/v1/auth/logout         # 用户登出
GET  /api/v1/auth/me             # 获取当前用户信息
POST /api/v1/auth/refresh        # 刷新访问令牌

GET    /api/v1/users             # 获取用户列表
POST   /api/v1/users             # 创建用户
GET    /api/v1/users/{id}        # 获取用户详情
PUT    /api/v1/users/{id}        # 更新用户
DELETE /api/v1/users/{id}        # 删除用户
```

### 知识点管理 API
```
GET    /api/v1/knowledge-points                    # 获取知识点列表
POST   /api/v1/knowledge-points                    # 创建知识点
GET    /api/v1/knowledge-points/{id}               # 获取知识点详情
PUT    /api/v1/knowledge-points/{id}               # 更新知识点
DELETE /api/v1/knowledge-points/{id}               # 删除知识点
GET    /api/v1/knowledge-points/{id}/children      # 获取子知识点
POST   /api/v1/knowledge-points/{id}/prerequisites # 添加前置关系
```

### 题目管理 API
```
GET    /api/v1/questions                 # 获取题目列表
POST   /api/v1/questions                 # 创建题目
GET    /api/v1/questions/{id}            # 获取题目详情
PUT    /api/v1/questions/{id}            # 更新题目
DELETE /api/v1/questions/{id}            # 删除题目
POST   /api/v1/questions/{id}/assets     # 上传题目资源
GET    /api/v1/questions/{id}/knowledge-points # 获取关联知识点
```

### 标注任务 API
```
GET    /api/v1/annotation-tasks                    # 获取标注任务列表
POST   /api/v1/annotation-tasks                    # 创建标注任务
GET    /api/v1/annotation-tasks/{id}               # 获取任务详情
PUT    /api/v1/annotation-tasks/{id}               # 更新任务
DELETE /api/v1/annotation-tasks/{id}               # 删除任务
POST   /api/v1/annotation-tasks/{id}/assign       # 分配任务
POST   /api/v1/annotation-tasks/{id}/submit       # 提交标注结果
POST   /api/v1/annotation-tasks/{id}/review       # 审核标注结果
```

### 质量检测 API
```
GET  /api/v1/quality/reports                      # 获取质量报告列表
POST /api/v1/quality/reports                      # 生成质量报告
GET  /api/v1/quality/reports/{id}                 # 获取质量报告详情
GET  /api/v1/quality/statistics                   # 获取质量统计数据
POST /api/v1/quality/detect-anomalies             # 检测异常数据
```

### 版本管理 API
```
GET    /api/v1/versions                           # 获取版本列表
POST   /api/v1/versions                           # 创建版本快照
GET    /api/v1/versions/{id}                      # 获取版本详情
POST   /api/v1/versions/{id}/restore              # 恢复版本
GET    /api/v1/versions/{id}/compare/{other_id}   # 版本比较
```

## 查询参数规范

### 分页参数
```
GET /api/v1/users?page=1&per_page=20
```

### 过滤参数
```
GET /api/v1/questions?type=SC&difficulty=easy&status=active
```

### 排序参数
```
GET /api/v1/users?sort=created_at&order=desc
```

### 搜索参数
```
GET /api/v1/knowledge-points?search=数学&fields=title,description
```

## 认证和授权

### JWT Token 认证
- 使用 Bearer Token 认证
- Token 包含用户信息和权限
- 实施 Token 刷新机制

```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 权限控制
- 基于角色的访问控制 (RBAC)
- API 端点权限检查
- 资源级别权限控制

## 错误处理

### 错误代码规范
```
AUTH_001: 认证失败
AUTH_002: 权限不足
VALIDATION_001: 数据验证失败
BUSINESS_001: 业务规则违反
SYSTEM_001: 系统内部错误
```

### 错误响应示例
```json
{
  "success": false,
  "error": {
    "code": "AUTH_001",
    "message": "用户名或密码错误",
    "timestamp": "2024-01-01T00:00:00Z",
    "path": "/api/v1/auth/login"
  }
}
```

## 性能优化

### 缓存策略
- 使用 HTTP 缓存头
- 实施 Redis 缓存
- 缓存失效策略

### 限流控制
- API 调用频率限制
- 用户级别限流
- 全局限流保护

### 数据压缩
- 响应数据 GZIP 压缩
- 大数据集分页返回
- 字段选择器减少数据传输

## 文档和测试

### API 文档
- 使用 OpenAPI/Swagger 规范
- 自动生成 API 文档
- 提供示例代码

### API 测试
- 单元测试覆盖所有端点
- 集成测试验证业务流程
- 性能测试确保响应时间
# API 设计规范

## RESTful API 设计原则

### URL 设计
- 使用名词而非动词
- 使用复数形式表示资源集合
- 使用层级结构表示资源关系
- 使用小写字母和连字符

```
✅ 正确示例:
GET /api/v1/users
GET /api/v1/users/123
GET /api/v1/users/123/knowledge-points
POST /api/v1/annotation-tasks

❌ 错误示例:
GET /api/v1/getUsers
GET /api/v1/user/123
GET /api/v1/users/123/getKnowledgePoints
```

### HTTP 方法使用
- `GET` - 获取资源
- `POST` - 创建资源
- `PUT` - 完整更新资源
- `PATCH` - 部分更新资源
- `DELETE` - 删除资源

### 状态码规范
- `200 OK` - 成功获取资源
- `201 Created` - 成功创建资源
- `204 No Content` - 成功删除资源
- `400 Bad Request` - 请求参数错误
- `401 Unauthorized` - 未认证
- `403 Forbidden` - 无权限
- `404 Not Found` - 资源不存在
- `422 Unprocessable Entity` - 验证失败
- `500 Internal Server Error` - 服务器错误

## API 版本管理
- 使用 URL 路径版本控制 (`/api/v1/`)
- 向后兼容原则
- 废弃版本提前通知

## 请求和响应格式

### 请求格式
```json
{
  "title": "数学基础",
  "description": "基础数学知识点",
  "parent_id": 1,
  "metadata": {
    "difficulty": "easy",
    "tags": ["math", "basic"]
  }
}
```

### 成功响应格式
```json
{
  "success": true,
  "data": {
    "id": 123,
    "title": "数学基础",
    "description": "基础数学知识点",
    "parent_id": 1,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  },
  "message": "操作成功"
}
```

### 错误响应格式
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "输入数据验证失败",
    "details": [
      {
        "field": "title",
        "message": "标题不能为空"
      }
    ]
  }
}
```

### 列表响应格式
```json
{
  "success": true,
  "data": {
    "items": [...],
    "pagination": {
      "page": 1,
      "per_page": 20,
      "total": 100,
      "total_pages": 5
    }
  }
}
```

## 业务模块 API 设计

### 用户管理 API
```
POST /api/v1/auth/login          # 用户登录
POST /api/v1/auth/logout         # 用户登出
GET  /api/v1/auth/me             # 获取当前用户信息
POST /api/v1/auth/refresh        # 刷新访问令牌

GET    /api/v1/users             # 获取用户列表
POST   /api/v1/users             # 创建用户
GET    /api/v1/users/{id}        # 获取用户详情
PUT    /api/v1/users/{id}        # 更新用户
DELETE /api/v1/users/{id}        # 删除用户
```

### 知识点管理 API
```
GET    /api/v1/knowledge-points                    # 获取知识点列表
POST   /api/v1/knowledge-points                    # 创建知识点
GET    /api/v1/knowledge-points/{id}               # 获取知识点详情
PUT    /api/v1/knowledge-points/{id}               # 更新知识点
DELETE /api/v1/knowledge-points/{id}               # 删除知识点
GET    /api/v1/knowledge-points/{id}/children      # 获取子知识点
POST   /api/v1/knowledge-points/{id}/prerequisites # 添加前置关系
```

### 题目管理 API
```
GET    /api/v1/questions                 # 获取题目列表
POST   /api/v1/questions                 # 创建题目
GET    /api/v1/questions/{id}            # 获取题目详情
PUT    /api/v1/questions/{id}            # 更新题目
DELETE /api/v1/questions/{id}            # 删除题目
POST   /api/v1/questions/{id}/assets     # 上传题目资源
GET    /api/v1/questions/{id}/knowledge-points # 获取关联知识点
```

### 标注任务 API
```
GET    /api/v1/annotation-tasks                    # 获取标注任务列表
POST   /api/v1/annotation-tasks                    # 创建标注任务
GET    /api/v1/annotation-tasks/{id}               # 获取任务详情
PUT    /api/v1/annotation-tasks/{id}               # 更新任务
DELETE /api/v1/annotation-tasks/{id}               # 删除任务
POST   /api/v1/annotation-tasks/{id}/assign       # 分配任务
POST   /api/v1/annotation-tasks/{id}/submit       # 提交标注结果
POST   /api/v1/annotation-tasks/{id}/review       # 审核标注结果
```

### 质量检测 API
```
GET  /api/v1/quality/reports                      # 获取质量报告列表
POST /api/v1/quality/reports                      # 生成质量报告
GET  /api/v1/quality/reports/{id}                 # 获取质量报告详情
GET  /api/v1/quality/statistics                   # 获取质量统计数据
POST /api/v1/quality/detect-anomalies             # 检测异常数据
```

### 版本管理 API
```
GET    /api/v1/versions                           # 获取版本列表
POST   /api/v1/versions                           # 创建版本快照
GET    /api/v1/versions/{id}                      # 获取版本详情
POST   /api/v1/versions/{id}/restore              # 恢复版本
GET    /api/v1/versions/{id}/compare/{other_id}   # 版本比较
```

## 查询参数规范

### 分页参数
```
GET /api/v1/users?page=1&per_page=20
```

### 过滤参数
```
GET /api/v1/questions?type=SC&difficulty=easy&status=active
```

### 排序参数
```
GET /api/v1/users?sort=created_at&order=desc
```

### 搜索参数
```
GET /api/v1/knowledge-points?search=数学&fields=title,description
```

## 认证和授权

### JWT Token 认证
- 使用 Bearer Token 认证
- Token 包含用户信息和权限
- 实施 Token 刷新机制

```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 权限控制
- 基于角色的访问控制 (RBAC)
- API 端点权限检查
- 资源级别权限控制

## 错误处理

### 错误代码规范
```
AUTH_001: 认证失败
AUTH_002: 权限不足
VALIDATION_001: 数据验证失败
BUSINESS_001: 业务规则违反
SYSTEM_001: 系统内部错误
```

### 错误响应示例
```json
{
  "success": false,
  "error": {
    "code": "AUTH_001",
    "message": "用户名或密码错误",
    "timestamp": "2024-01-01T00:00:00Z",
    "path": "/api/v1/auth/login"
  }
}
```

## 性能优化

### 缓存策略
- 使用 HTTP 缓存头
- 实施 Redis 缓存
- 缓存失效策略

### 限流控制
- API 调用频率限制
- 用户级别限流
- 全局限流保护

### 数据压缩
- 响应数据 GZIP 压缩
- 大数据集分页返回
- 字段选择器减少数据传输

## 文档和测试

### API 文档
- 使用 OpenAPI/Swagger 规范
- 自动生成 API 文档
- 提供示例代码

### API 测试
- 单元测试覆盖所有端点
- 集成测试验证业务流程
- 性能测试确保响应时间
