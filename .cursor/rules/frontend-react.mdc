---
alwaysApply: true
---

# 前端 React/TypeScript 开发规则

## 技术栈
- React 18 + TypeScript
- Vite 作为构建工具
- Tailwind CSS 用于样式
- shadcn/ui 组件库
- Zustand 状态管理
- React Router 路由管理
- React Hook Form 表单处理

## 组件开发规范

### 组件结构
- 使用函数组件和 React Hooks
- 组件文件名使用 kebab-case (如 `user-form.tsx`)
- 组件名使用 PascalCase (如 `UserForm`)
- 每个组件放在独立的文件中

```typescript
// components/users/user-form.tsx
import { useState } from 'react';
import { User } from '@/types';

interface UserFormProps {
  user?: User;
  onSubmit: (user: User) => void;
}

export const UserForm: React.FC<UserFormProps> = ({ user, onSubmit }) => {
  // 组件实现
  return (
    <div>
      {/* JSX 内容 */}
    </div>
  );
};
```

### 组件组织
- 按功能模块组织组件目录
- 可复用组件放在 [components/ui/](mdc:frontend/src/components/ui/) 目录
- 业务组件按模块分组 (如 [components/users/](mdc:frontend/src/components/users/))
- 页面组件放在 [pages/](mdc:frontend/src/pages/) 目录

### Props 和 State
- 使用 TypeScript 接口定义 Props 类型
- 优先使用 React Hooks 管理状态
- 避免过度使用 useEffect，优先使用其他 hooks

## 样式规范

### Tailwind CSS
- 使用 Tailwind 原子类进行样式设计
- 避免内联样式，使用 Tailwind 类名
- 使用 `cn()` 工具函数合并类名

```typescript
import { cn } from '@/lib/utils';

<div className={cn(
  "flex items-center justify-between p-4",
  "bg-white rounded-lg shadow-md",
  isActive && "bg-blue-50 border-blue-200"
)}>
```

### 组件样式
- 使用 shadcn/ui 组件作为基础
- 自定义组件继承 shadcn/ui 的设计系统
- 保持设计一致性

## 状态管理

### Zustand Store
- 使用 Zustand 管理全局状态
- Store 按功能模块分离
- 使用 TypeScript 类型定义 Store

```typescript
// stores/auth.ts
import { create } from 'zustand';
import { User } from '@/types';

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  login: (user: User) => void;
  logout: () => void;
}

export const useAuthStore = create<AuthState>((set) => ({
  user: null,
  isAuthenticated: false,
  login: (user) => set({ user, isAuthenticated: true }),
  logout: () => set({ user: null, isAuthenticated: false }),
}));
```

### 本地状态
- 简单的组件状态使用 `useState`
- 复杂的状态逻辑使用 `useReducer`
- 表单状态使用 React Hook Form

## API 服务

### 服务层组织
- API 调用封装在 [services/](mdc:frontend/src/services/) 目录
- 每个业务模块对应一个服务文件
- 使用统一的 HTTP 客户端

```typescript
// services/users.ts
import { apiClient } from './api';
import { User, CreateUserRequest } from '@/types';

export const userService = {
  getUsers: (): Promise<User[]> => 
    apiClient.get('/users').then(res => res.data),
    
  createUser: (data: CreateUserRequest): Promise<User> =>
    apiClient.post('/users', data).then(res => res.data),
};
```

### 错误处理
- 统一的错误处理机制
- 使用 toast 组件显示错误消息
- 网络错误和业务错误分别处理

## 类型定义

### TypeScript 类型
- 所有类型定义放在 [types/](mdc:frontend/src/types/) 目录
- 使用接口定义对象类型
- 使用联合类型定义枚举值

```typescript
// types/index.ts
export interface User {
  id: number;
  username: string;
  email: string;
  role: UserRole;
  created_at: string;
  updated_at: string;
}

export type UserRole = 'admin' | 'annotator' | 'reviewer' | 'viewer';

export interface CreateUserRequest {
  username: string;
  email: string;
  password: string;
  role: UserRole;
}
```

## 路由管理

### React Router
- 使用 React Router v6 进行路由管理
- 路由配置集中管理
- 实施路由守卫进行权限控制

```typescript
// 使用 ProtectedRoute 组件保护需要认证的路由
<Route 
  path="/admin" 
  element={
    <ProtectedRoute requiredRole="admin">
      <AdminPage />
    </ProtectedRoute>
  } 
/>
```

## 表单处理

### React Hook Form
- 使用 React Hook Form 处理表单
- 结合 Zod 进行表单验证
- 使用 shadcn/ui 的 Form 组件

```typescript
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

const formSchema = z.object({
  username: z.string().min(3, '用户名至少3个字符'),
  email: z.string().email('邮箱格式不正确'),
});

const UserForm = () => {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
  });
  
  // 表单处理逻辑
};
```

## 性能优化

### 组件优化
- 使用 `React.memo` 优化组件重渲染
- 使用 `useMemo` 和 `useCallback` 优化计算和函数
- 实施代码分割和懒加载

### 数据获取
- 使用 React Query 或 SWR 进行数据缓存
- 实施分页和虚拟滚动
- 优化图片和资源加载

## 测试规范

### 单元测试
- 使用 Vitest 进行单元测试
- 测试组件的核心功能
- 模拟 API 调用和外部依赖

### 集成测试
- 使用 Testing Library 进行组件测试
- 测试用户交互和业务流程
- 保持测试覆盖率

## 代码质量

### ESLint 和 Prettier
- 遵循 ESLint 规则
- 使用 Prettier 格式化代码
- 提交前运行代码检查

### 文件组织
- 使用绝对路径导入 (`@/components/...`)
- 保持导入语句有序
- 删除未使用的导入和变量
# 前端 React/TypeScript 开发规则

## 技术栈
- React 18 + TypeScript
- Vite 作为构建工具
- Tailwind CSS 用于样式
- shadcn/ui 组件库
- Zustand 状态管理
- React Router 路由管理
- React Hook Form 表单处理

## 组件开发规范

### 组件结构
- 使用函数组件和 React Hooks
- 组件文件名使用 kebab-case (如 `user-form.tsx`)
- 组件名使用 PascalCase (如 `UserForm`)
- 每个组件放在独立的文件中

```typescript
// components/users/user-form.tsx
import { useState } from 'react';
import { User } from '@/types';

interface UserFormProps {
  user?: User;
  onSubmit: (user: User) => void;
}

export const UserForm: React.FC<UserFormProps> = ({ user, onSubmit }) => {
  // 组件实现
  return (
    <div>
      {/* JSX 内容 */}
    </div>
  );
};
```

### 组件组织
- 按功能模块组织组件目录
- 可复用组件放在 [components/ui/](mdc:frontend/src/components/ui/) 目录
- 业务组件按模块分组 (如 [components/users/](mdc:frontend/src/components/users/))
- 页面组件放在 [pages/](mdc:frontend/src/pages/) 目录

### Props 和 State
- 使用 TypeScript 接口定义 Props 类型
- 优先使用 React Hooks 管理状态
- 避免过度使用 useEffect，优先使用其他 hooks

## 样式规范

### Tailwind CSS
- 使用 Tailwind 原子类进行样式设计
- 避免内联样式，使用 Tailwind 类名
- 使用 `cn()` 工具函数合并类名

```typescript
import { cn } from '@/lib/utils';

<div className={cn(
  "flex items-center justify-between p-4",
  "bg-white rounded-lg shadow-md",
  isActive && "bg-blue-50 border-blue-200"
)}>
```

### 组件样式
- 使用 shadcn/ui 组件作为基础
- 自定义组件继承 shadcn/ui 的设计系统
- 保持设计一致性

## 状态管理

### Zustand Store
- 使用 Zustand 管理全局状态
- Store 按功能模块分离
- 使用 TypeScript 类型定义 Store

```typescript
// stores/auth.ts
import { create } from 'zustand';
import { User } from '@/types';

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  login: (user: User) => void;
  logout: () => void;
}

export const useAuthStore = create<AuthState>((set) => ({
  user: null,
  isAuthenticated: false,
  login: (user) => set({ user, isAuthenticated: true }),
  logout: () => set({ user: null, isAuthenticated: false }),
}));
```

### 本地状态
- 简单的组件状态使用 `useState`
- 复杂的状态逻辑使用 `useReducer`
- 表单状态使用 React Hook Form

## API 服务

### 服务层组织
- API 调用封装在 [services/](mdc:frontend/src/services/) 目录
- 每个业务模块对应一个服务文件
- 使用统一的 HTTP 客户端

```typescript
// services/users.ts
import { apiClient } from './api';
import { User, CreateUserRequest } from '@/types';

export const userService = {
  getUsers: (): Promise<User[]> => 
    apiClient.get('/users').then(res => res.data),
    
  createUser: (data: CreateUserRequest): Promise<User> =>
    apiClient.post('/users', data).then(res => res.data),
};
```

### 错误处理
- 统一的错误处理机制
- 使用 toast 组件显示错误消息
- 网络错误和业务错误分别处理

## 类型定义

### TypeScript 类型
- 所有类型定义放在 [types/](mdc:frontend/src/types/) 目录
- 使用接口定义对象类型
- 使用联合类型定义枚举值

```typescript
// types/index.ts
export interface User {
  id: number;
  username: string;
  email: string;
  role: UserRole;
  created_at: string;
  updated_at: string;
}

export type UserRole = 'admin' | 'annotator' | 'reviewer' | 'viewer';

export interface CreateUserRequest {
  username: string;
  email: string;
  password: string;
  role: UserRole;
}
```

## 路由管理

### React Router
- 使用 React Router v6 进行路由管理
- 路由配置集中管理
- 实施路由守卫进行权限控制

```typescript
// 使用 ProtectedRoute 组件保护需要认证的路由
<Route 
  path="/admin" 
  element={
    <ProtectedRoute requiredRole="admin">
      <AdminPage />
    </ProtectedRoute>
  } 
/>
```

## 表单处理

### React Hook Form
- 使用 React Hook Form 处理表单
- 结合 Zod 进行表单验证
- 使用 shadcn/ui 的 Form 组件

```typescript
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

const formSchema = z.object({
  username: z.string().min(3, '用户名至少3个字符'),
  email: z.string().email('邮箱格式不正确'),
});

const UserForm = () => {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
  });
  
  // 表单处理逻辑
};
```

## 性能优化

### 组件优化
- 使用 `React.memo` 优化组件重渲染
- 使用 `useMemo` 和 `useCallback` 优化计算和函数
- 实施代码分割和懒加载

### 数据获取
- 使用 React Query 或 SWR 进行数据缓存
- 实施分页和虚拟滚动
- 优化图片和资源加载

## 测试规范

### 单元测试
- 使用 Vitest 进行单元测试
- 测试组件的核心功能
- 模拟 API 调用和外部依赖

### 集成测试
- 使用 Testing Library 进行组件测试
- 测试用户交互和业务流程
- 保持测试覆盖率

## 代码质量

### ESLint 和 Prettier
- 遵循 ESLint 规则
- 使用 Prettier 格式化代码
- 提交前运行代码检查

### 文件组织
- 使用绝对路径导入 (`@/components/...`)
- 保持导入语句有序
- 删除未使用的导入和变量
