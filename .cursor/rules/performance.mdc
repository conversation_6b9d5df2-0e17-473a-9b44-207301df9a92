---
alwaysApply: true
---

# 性能优化规范

## 数据库性能优化

### 查询优化
- 使用适当的索引策略
- 避免 N+1 查询问题
- 使用连接查询代替循环查询
- 实施查询缓存机制

```python
# 避免 N+1 查询的示例
from sqlalchemy.orm import joinedload, selectinload

# ❌ 错误示例 - 会产生 N+1 查询
def get_questions_with_knowledge_points_bad(db: Session):
    questions = db.query(Question).all()
    for question in questions:
        # 每个问题都会执行一次查询
        knowledge_points = question.knowledge_points
    return questions

# ✅ 正确示例 - 使用 joinedload
def get_questions_with_knowledge_points_good(db: Session):
    return db.query(Question).options(
        joinedload(Question.knowledge_points)
    ).all()

# ✅ 对于一对多关系，使用 selectinload
def get_knowledge_points_with_questions(db: Session):
    return db.query(KnowledgePoint).options(
        selectinload(KnowledgePoint.questions)
    ).all()
```

### 索引策略
- 为频繁查询的字段创建索引
- 使用复合索引优化多字段查询
- 定期分析索引使用情况

```sql
-- 为常用查询字段创建索引
CREATE INDEX idx_questions_type ON questions(type);
CREATE INDEX idx_questions_difficulty ON questions(difficulty);
CREATE INDEX idx_questions_created_at ON questions(created_at);

-- 复合索引用于多字段查询
CREATE INDEX idx_questions_type_difficulty ON questions(type, difficulty);
CREATE INDEX idx_annotation_tasks_assignee_status ON annotation_tasks(assignee_id, status);

-- 全文搜索索引
CREATE INDEX idx_questions_content_gin ON questions USING gin(to_tsvector('english', content));
CREATE INDEX idx_knowledge_points_title_gin ON knowledge_points USING gin(to_tsvector('english', title));
```

### 分页优化
- 使用游标分页代替偏移分页
- 限制单页数据量
- 实施深度分页保护

```python
from typing import Optional

def get_questions_paginated(
    db: Session,
    page: int = 1,
    per_page: int = 20,
    cursor: Optional[int] = None
) -> dict:
    """优化的分页查询"""
    # 限制每页数据量
    per_page = min(per_page, 100)
    
    query = db.query(Question)
    
    # 使用游标分页（推荐用于大数据集）
    if cursor:
        query = query.filter(Question.id > cursor)
    else:
        # 传统偏移分页（适用于小数据集）
        offset = (page - 1) * per_page
        query = query.offset(offset)
    
    questions = query.limit(per_page).all()
    
    # 返回下一页游标
    next_cursor = questions[-1].id if questions else None
    
    return {
        "items": questions,
        "next_cursor": next_cursor,
        "has_more": len(questions) == per_page
    }
```

## 缓存策略

### Redis 缓存
- 缓存频繁访问的数据
- 实施缓存失效策略
- 使用缓存预热

```python
import redis
import json
from typing import Optional, Any

redis_client = redis.Redis(host='localhost', port=6379, db=0)

class CacheService:
    def __init__(self):
        self.default_ttl = 3600  # 1小时
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存数据"""
        try:
            data = redis_client.get(key)
            return json.loads(data) if data else None
        except Exception:
            return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存数据"""
        try:
            ttl = ttl or self.default_ttl
            redis_client.setex(key, ttl, json.dumps(value))
            return True
        except Exception:
            return False
    
    def delete(self, key: str) -> bool:
        """删除缓存数据"""
        try:
            redis_client.delete(key)
            return True
        except Exception:
            return False
    
    def get_or_set(self, key: str, func, ttl: Optional[int] = None) -> Any:
        """获取缓存，如果不存在则执行函数并缓存结果"""
        data = self.get(key)
        if data is None:
            data = func()
            self.set(key, data, ttl)
        return data

# 使用示例
cache = CacheService()

def get_knowledge_points_cached(db: Session) -> List[dict]:
    """缓存知识点列表"""
    return cache.get_or_set(
        "knowledge_points:all",
        lambda: [kp.to_dict() for kp in db.query(KnowledgePoint).all()],
        ttl=1800  # 30分钟
    )
```

### 应用层缓存
- 使用内存缓存存储计算结果
- 实施 LRU 缓存淘汰策略
- 缓存配置和常量数据

```python
from functools import lru_cache
from typing import List, Dict

class ConfigCache:
    """配置缓存"""
    _instance = None
    _cache = {}
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    @lru_cache(maxsize=128)
    def get_user_permissions(self, user_role: str) -> List[str]:
        """获取用户权限（带缓存）"""
        return ROLE_PERMISSIONS.get(user_role, [])
    
    @lru_cache(maxsize=64)
    def get_question_types(self) -> List[str]:
        """获取题目类型列表（带缓存）"""
        return [t.value for t in QuestionType]
```

## 前端性能优化

### 组件优化
- 使用 React.memo 防止不必要的重渲染
- 实施代码分割和懒加载
- 优化状态管理

```typescript
import React, { memo, useMemo, useCallback } from 'react';

// 使用 memo 优化组件
const QuestionItem = memo<QuestionItemProps>(({ question, onEdit }) => {
  // 使用 useMemo 优化计算
  const formattedContent = useMemo(() => {
    return formatQuestionContent(question.content);
  }, [question.content]);
  
  // 使用 useCallback 优化事件处理
  const handleEdit = useCallback(() => {
    onEdit(question.id);
  }, [question.id, onEdit]);
  
  return (
    <div>
      <h3>{question.title}</h3>
      <div>{formattedContent}</div>
      <button onClick={handleEdit}>编辑</button>
    </div>
  );
});

// 代码分割和懒加载
const AdvancedVisualization = lazy(() => import('./AdvancedVisualization'));

const App = () => {
  return (
    <Suspense fallback={<div>加载中...</div>}>
      <AdvancedVisualization />
    </Suspense>
  );
};
```

### 数据获取优化
- 实施数据预加载
- 使用虚拟滚动处理大列表
- 优化 API 调用

```typescript
import { useVirtualizer } from '@tanstack/react-virtual';

const VirtualizedQuestionList: React.FC<{ questions: Question[] }> = ({ questions }) => {
  const parentRef = useRef<HTMLDivElement>(null);
  
  const virtualizer = useVirtualizer({
    count: questions.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 100, // 估算每项高度
    overscan: 5, // 预渲染项目数
  });
  
  return (
    <div ref={parentRef} style={{ height: '400px', overflow: 'auto' }}>
      <div style={{ height: virtualizer.getTotalSize(), position: 'relative' }}>
        {virtualizer.getVirtualItems().map((virtualRow) => (
          <div
            key={virtualRow.index}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: virtualRow.size,
              transform: `translateY(${virtualRow.start}px)`,
            }}
          >
            <QuestionItem question={questions[virtualRow.index]} />
          </div>
        ))}
      </div>
    </div>
  );
};
```

## API 性能优化

### 响应优化
- 实施 GZIP 压缩
- 使用 HTTP/2
- 优化 JSON 序列化

```python
from fastapi import FastAPI
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
import orjson

app = FastAPI()

# 启用 GZIP 压缩
app.add_middleware(GZipMiddleware, minimum_size=1000)

class ORJSONResponse(JSONResponse):
    """使用 orjson 优化 JSON 序列化性能"""
    media_type = "application/json"

    def render(self, content: any) -> bytes:
        return orjson.dumps(content)

# 使用优化的 JSON 响应
@app.get("/api/v1/questions", response_class=ORJSONResponse)
async def get_questions():
    # 返回数据
    pass
```

### 并发处理
- 使用异步处理
- 实施连接池
- 优化数据库连接

```python
import asyncio
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

# 异步数据库引擎
async_engine = create_async_engine(
    "postgresql+asyncpg://user:password@localhost/db",
    pool_size=20,
    max_overflow=30,
    pool_pre_ping=True
)

AsyncSessionLocal = sessionmaker(
    async_engine, class_=AsyncSession, expire_on_commit=False
)

async def get_questions_async(db: AsyncSession) -> List[Question]:
    """异步查询题目"""
    result = await db.execute(select(Question))
    return result.scalars().all()

# 并发处理多个请求
async def process_multiple_requests(request_ids: List[int]):
    """并发处理多个请求"""
    tasks = []
    for request_id in request_ids:
        task = asyncio.create_task(process_single_request(request_id))
        tasks.append(task)
    
    results = await asyncio.gather(*tasks, return_exceptions=True)
    return results
```

## 监控和分析

### 性能监控
- 监控 API 响应时间
- 跟踪数据库查询性能
- 监控内存和 CPU 使用率

```python
import time
import logging
from functools import wraps

def monitor_performance(func):
    """性能监控装饰器"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            # 记录性能日志
            logging.info(f"{func.__name__} executed in {execution_time:.2f}s")
            
            # 如果执行时间过长，记录警告
            if execution_time > 5.0:
                logging.warning(f"Slow query detected: {func.__name__} took {execution_time:.2f}s")
            
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logging.error(f"{func.__name__} failed after {execution_time:.2f}s: {str(e)}")
            raise
    return wrapper

# 使用示例
@monitor_performance
async def get_complex_data(db: Session):
    # 复杂的数据查询
    pass
```

### 查询分析
- 使用 EXPLAIN 分析查询计划
- 监控慢查询
- 优化索引使用

```python
import logging
from sqlalchemy import event
from sqlalchemy.engine import Engine

# 监控慢查询
@event.listens_for(Engine, "before_cursor_execute")
def receive_before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    context._query_start_time = time.time()

@event.listens_for(Engine, "after_cursor_execute")
def receive_after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    total = time.time() - context._query_start_time
    if total > 0.5:  # 超过 0.5 秒的查询
        logging.warning(f"Slow query: {total:.2f}s - {statement[:100]}...")
```

## 资源优化

### 内存优化
- 避免内存泄漏
- 使用生成器处理大数据集
- 实施内存监控

```python
import gc
import psutil
from typing import Generator

def process_large_dataset(db: Session) -> Generator[dict, None, None]:
    """使用生成器处理大数据集，避免内存溢出"""
    batch_size = 1000
    offset = 0
    
    while True:
        questions = db.query(Question).offset(offset).limit(batch_size).all()
        if not questions:
            break
        
        for question in questions:
            yield question.to_dict()
        
        offset += batch_size
        
        # 定期清理内存
        if offset % 10000 == 0:
            gc.collect()

def monitor_memory_usage():
    """监控内存使用情况"""
    process = psutil.Process()
    memory_info = process.memory_info()
    
    logging.info(f"Memory usage: {memory_info.rss / 1024 / 1024:.2f} MB")
    
    # 如果内存使用过高，记录警告
    if memory_info.rss > 1024 * 1024 * 1024:  # 1GB
        logging.warning("High memory usage detected")
```

### 网络优化
- 使用 CDN 加速静态资源
- 实施资源压缩
- 优化图片和媒体文件

```python
from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
import os

app = FastAPI()

# 配置静态文件服务
app.mount("/static", StaticFiles(directory="static"), name="static")

@app.get("/api/v1/questions/{question_id}/image")
async def get_question_image(question_id: int):
    """优化图片响应"""
    image_path = f"static/images/questions/{question_id}.jpg"
    
    if os.path.exists(image_path):
        return FileResponse(
            image_path,
            media_type="image/jpeg",
            headers={
                "Cache-Control": "public, max-age=3600",  # 缓存1小时
                "ETag": f'"{question_id}"'
            }
        )
    else:
        raise HTTPException(status_code=404, detail="图片不存在")
```

## 性能测试

### 负载测试
- 使用 Locust 进行负载测试
- 测试并发用户场景
- 监控系统瓶颈

```python
# locustfile.py
from locust import HttpUser, task, between

class AnnotationSystemUser(HttpUser):
    wait_time = between(1, 3)
    
    def on_start(self):
        """登录用户"""
        response = self.client.post("/api/v1/auth/login", json={
            "username": "testuser",
            "password": "testpass"
        })
        if response.status_code == 200:
            self.token = response.json()["access_token"]
            self.headers = {"Authorization": f"Bearer {self.token}"}
    
    @task(3)
    def get_questions(self):
        """获取题目列表"""
        self.client.get("/api/v1/questions", headers=self.headers)
    
    @task(2)
    def get_knowledge_points(self):
        """获取知识点"""
        self.client.get("/api/v1/knowledge-points", headers=self.headers)
    
    @task(1)
    def create_annotation(self):
        """创建标注"""
        self.client.post("/api/v1/annotations", 
                        json={"question_id": 1, "knowledge_points": [1, 2]},
                        headers=self.headers)
```

### 基准测试
- 建立性能基准
- 定期进行性能回归测试
- 监控关键指标趋势

```bash
# 运行负载测试
locust -f locustfile.py --host=http://localhost:8000

# 数据库性能测试
pgbench -c 10 -j 2 -t 1000 your_database

# 前端性能测试
lighthouse --output=json --output-path=./lighthouse-report.json http://localhost:3000
```
description:
globs:
alwaysApply: false
---
