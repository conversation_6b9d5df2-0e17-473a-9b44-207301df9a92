# 高级可视化功能说明

## 概述

本文档描述了数据标注系统中的高级可视化功能，包括知识图谱可视化、交互式网络图、动态图表、3D可视化等核心组件的实现和使用方法。

## 功能特性

### 1. 知识图谱可视化 (KnowledgeGraph)

#### 1.1 2D知识图谱
- **力导向布局**: 使用D3.js实现的物理模拟布局
- **交互功能**: 节点拖拽、缩放、平移、选择
- **动态控制**: 实时调整连接距离、排斥力、节点大小
- **高亮效果**: 悬停高亮相关节点和连接
- **标签显示**: 可切换节点标签显示/隐藏
- **图例支持**: 自动生成节点类型和连接类型图例

#### 1.2 核心特性
```typescript
interface KnowledgeNode {
  id: string
  name: string
  type: 'knowledge_point' | 'question' | 'concept'
  level: number
  weight: number
  group: string
  description?: string
}

interface KnowledgeLink {
  source: string
  target: string
  type: 'prerequisite' | 'related' | 'contains' | 'similar'
  weight: number
  strength: number
}
```

### 2. 3D知识图谱可视化 (KnowledgeGraph3D)

#### 2.1 3D渲染特性
- **Three.js渲染**: 基于WebGL的高性能3D渲染
- **VR支持**: 可选的虚拟现实模式
- **粒子效果**: 连接线上的动态粒子流
- **相机控制**: 轨道控制、聚焦、自动旋转
- **光照效果**: 动态光照和阴影

#### 2.2 交互功能
- **节点聚焦**: 点击节点自动聚焦相机
- **高亮系统**: 3D环境下的节点关系高亮
- **控制面板**: 实时调整渲染参数
- **导出功能**: 支持3D场景截图导出

### 3. 高级数据分析仪表板 (AdvancedAnalyticsDashboard)

#### 3.1 多维度分析
- **概览指标**: 关键性能指标的实时监控
- **趋势分析**: 时间序列数据的多维度展示
- **分布分析**: 数据分布的可视化呈现
- **性能分析**: 系统性能的热力图展示
- **高级分析**: 散点图、相关性分析

#### 3.2 图表类型
- **组合图表**: 柱状图、折线图、面积图的组合
- **雷达图**: 多维度性能评估
- **树状图**: 层次数据的空间填充展示
- **漏斗图**: 流程转化率分析
- **热力图**: 二维数据密度展示

### 4. 交互式网络图 (InteractiveNetwork)

#### 4.1 网络布局
- **力导向布局**: 自然的节点分布
- **层次布局**: 树状结构的清晰展示
- **随机布局**: 快速原型和测试
- **自定义布局**: 支持用户定义的布局算法

#### 4.2 高级功能
- **节点聚类**: 动态创建和展开节点集群
- **多选操作**: 支持Ctrl+点击多选节点
- **实时编辑**: 动态添加/删除节点和连接
- **物理引擎**: 可调节的物理模拟参数
- **导出支持**: PNG格式的网络图导出

### 5. 动态图表 (AnimatedCharts)

#### 5.1 动画特性
- **帧动画**: 基于时间序列的数据帧切换
- **过渡动画**: 平滑的数据变化过渡效果
- **播放控制**: 播放、暂停、快进、倒退
- **速度控制**: 可调节的播放速度
- **进度控制**: 拖拽式的进度条控制

#### 5.2 图表类型
- **柱状图动画**: 数据变化的柱状图展示
- **折线图动画**: 趋势变化的动态展示
- **饼图动画**: 比例变化的动态饼图
- **散点图动画**: 数据点的动态分布
- **径向图动画**: 圆形布局的动态展示

## 技术实现

### 1. 核心技术栈

#### 1.1 可视化库
```json
{
  "d3": "^7.8.5",
  "react-force-graph": "^1.44.4",
  "react-force-graph-3d": "^1.24.4",
  "three": "^0.158.0",
  "vis-network": "^9.1.9",
  "recharts": "^2.8.0"
}
```

#### 1.2 动画库
```json
{
  "framer-motion": "^10.16.16",
  "react-spring": "^9.7.3",
  "react-transition-group": "^4.4.5"
}
```

### 2. 组件架构

#### 2.1 可视化组件层次
```
AdvancedVisualizationPage
├── KnowledgeGraph (2D)
├── KnowledgeGraph3D (3D)
├── AdvancedAnalyticsDashboard
├── InteractiveNetwork
└── AnimatedCharts
```

#### 2.2 工具类支持
```
utils/visualization.ts
├── DataTransformer (数据转换)
├── LayoutAlgorithms (布局算法)
├── AnimationUtils (动画工具)
└── ExportUtils (导出工具)
```

### 3. 数据流架构

#### 3.1 数据处理流程
1. **原始数据** → **数据清洗** → **格式转换**
2. **格式转换** → **布局计算** → **渲染准备**
3. **渲染准备** → **可视化渲染** → **交互响应**

#### 3.2 状态管理
- **组件状态**: React useState/useEffect
- **动画状态**: Framer Motion/React Spring
- **渲染状态**: D3.js/Three.js内部状态

## 使用指南

### 1. 知识图谱可视化

#### 1.1 基本使用
```typescript
import { KnowledgeGraph } from '@/components/visualization/knowledge-graph'

const data = {
  nodes: [
    { id: '1', name: '线性代数', type: 'knowledge_point', level: 1, weight: 10, group: 'math' },
    { id: '2', name: '矩阵运算', type: 'knowledge_point', level: 2, weight: 8, group: 'math' }
  ],
  links: [
    { source: '1', target: '2', type: 'prerequisite', weight: 0.8, strength: 0.7 }
  ]
}

<KnowledgeGraph
  data={data}
  width={800}
  height={600}
  onNodeClick={(node) => console.log('Clicked:', node)}
  interactive={true}
  showLabels={true}
/>
```

#### 1.2 3D版本使用
```typescript
import { KnowledgeGraph3D } from '@/components/visualization/knowledge-graph-3d'

<KnowledgeGraph3D
  data={data}
  width={800}
  height={600}
  enableVR={true}
  backgroundColor="#000011"
/>
```

### 2. 数据分析仪表板

#### 2.1 配置数据
```typescript
const analyticsData = {
  overview: {
    totalQuestions: 1234,
    totalKnowledgePoints: 567,
    annotationCoverage: 85,
    qualityScore: 8.7
  },
  trends: [
    { date: '2024-01-01', questions: 100, quality: 85 },
    { date: '2024-01-02', questions: 120, quality: 87 }
  ],
  distribution: [
    { name: '优秀', value: 45, color: '#00C49F' },
    { name: '良好', value: 30, color: '#0088FE' }
  ]
}

<AdvancedAnalyticsDashboard
  data={analyticsData}
  timeRange="7d"
  onExport={(format) => console.log('Export:', format)}
/>
```

### 3. 交互式网络图

#### 3.1 网络配置
```typescript
const networkData = {
  nodes: [
    { id: '1', label: '节点1', group: 'type1', size: 20 },
    { id: '2', label: '节点2', group: 'type2', size: 15 }
  ],
  edges: [
    { id: 'e1', from: '1', to: '2', weight: 0.8 }
  ]
}

<InteractiveNetwork
  nodes={networkData.nodes}
  edges={networkData.edges}
  layout="force"
  physics={true}
  clustering={true}
/>
```

### 4. 动态图表

#### 4.1 动画数据
```typescript
const animatedData = [
  [
    { id: '1', name: 'A', value: 10, color: '#8884d8', timestamp: 1 },
    { id: '2', name: 'B', value: 20, color: '#82ca9d', timestamp: 1 }
  ],
  [
    { id: '1', name: 'A', value: 15, color: '#8884d8', timestamp: 2 },
    { id: '2', name: 'B', value: 25, color: '#82ca9d', timestamp: 2 }
  ]
]

<AnimatedCharts
  data={animatedData}
  chartType="bar"
  autoPlay={true}
  animationDuration={1000}
  onDataChange={(currentData, frameIndex) => {
    console.log('Frame:', frameIndex, 'Data:', currentData)
  }}
/>
```

## 性能优化

### 1. 渲染优化
- **虚拟化**: 大数据集的虚拟滚动
- **LOD**: 3D场景的细节层次控制
- **批处理**: DOM操作的批量处理
- **缓存**: 计算结果的智能缓存

### 2. 内存管理
- **对象池**: 重复使用的对象池化
- **垃圾回收**: 及时清理不用的引用
- **事件清理**: 组件卸载时的事件监听器清理

### 3. 交互优化
- **防抖**: 高频事件的防抖处理
- **节流**: 动画帧的节流控制
- **异步**: 耗时计算的异步处理

## 扩展开发

### 1. 自定义可视化组件
```typescript
interface CustomVisualizationProps {
  data: any[]
  config: VisualizationConfig
  onInteraction?: (event: InteractionEvent) => void
}

export function CustomVisualization({ data, config, onInteraction }: CustomVisualizationProps) {
  // 实现自定义可视化逻辑
}
```

### 2. 布局算法扩展
```typescript
export class CustomLayoutAlgorithm {
  static customLayout(nodes: any[], options: any) {
    // 实现自定义布局算法
    return processedNodes
  }
}
```

### 3. 数据转换器扩展
```typescript
export class CustomDataTransformer {
  static transformData(rawData: any[], transformConfig: any) {
    // 实现自定义数据转换逻辑
    return transformedData
  }
}
```

## 故障排除

### 1. 常见问题
- **渲染性能**: 检查数据量和渲染复杂度
- **内存泄漏**: 检查事件监听器和定时器清理
- **兼容性**: 检查浏览器WebGL支持

### 2. 调试工具
- **性能监控**: 使用浏览器性能工具
- **内存分析**: 使用内存分析器
- **可视化调试**: 使用D3.js调试工具

## 未来规划

### 1. 功能扩展
- **AR/VR支持**: 增强现实和虚拟现实支持
- **机器学习**: 智能布局和推荐算法
- **协作功能**: 多用户实时协作编辑
- **云端渲染**: 服务端渲染支持

### 2. 性能提升
- **WebAssembly**: 计算密集型任务优化
- **Web Workers**: 后台线程计算
- **GPU加速**: WebGL计算着色器
- **流式渲染**: 大数据集的流式处理
