# Makefile for Backend Testing

.PHONY: help install test test-unit test-integration test-api test-all test-coverage test-fast quality format clean

# 默认目标
help:
	@echo "后端测试 Makefile"
	@echo ""
	@echo "可用命令:"
	@echo "  install          - 安装依赖"
	@echo "  test             - 运行所有测试"
	@echo "  test-unit        - 运行单元测试"
	@echo "  test-integration - 运行集成测试"
	@echo "  test-api         - 运行API测试"
	@echo "  test-coverage    - 运行测试并生成覆盖率报告"
	@echo "  test-fast        - 运行快速测试（跳过慢速测试）"
	@echo "  quality          - 检查代码质量"
	@echo "  format           - 格式化代码"
	@echo "  clean            - 清理测试文件"

# 安装依赖
install:
	@echo "安装依赖..."
	pip install -r requirements.txt
	pip install -e ".[test,dev]"

# 设置测试环境
setup-test-env:
	@echo "设置测试环境..."
	@export TESTING=true
	@export DATABASE_URL=sqlite:///./test.db
	@if [ -f test.db ]; then rm test.db; fi

# 运行所有测试
test: setup-test-env
	@echo "运行所有测试..."
	python -m pytest tests/ -v

# 运行单元测试
test-unit: setup-test-env
	@echo "运行单元测试..."
	python -m pytest tests/unit/ -v -m unit

# 运行集成测试
test-integration: setup-test-env
	@echo "运行集成测试..."
	python -m pytest tests/integration/ -v -m integration

# 运行API测试
test-api: setup-test-env
	@echo "运行API测试..."
	python -m pytest tests/api/ -v -m api

# 运行所有测试并生成覆盖率报告
test-coverage: setup-test-env
	@echo "运行测试并生成覆盖率报告..."
	python -m pytest tests/ \
		--cov=app \
		--cov-report=term-missing \
		--cov-report=html \
		--cov-report=xml \
		-v
	@echo "覆盖率报告生成完成: htmlcov/index.html"

# 运行快速测试
test-fast: setup-test-env
	@echo "运行快速测试..."
	python -m pytest tests/ -v -m "not slow"

# 运行特定模块测试
test-models: setup-test-env
	@echo "运行模型测试..."
	python -m pytest tests/unit/models/ -v

test-services: setup-test-env
	@echo "运行服务测试..."
	python -m pytest tests/unit/services/ -v

test-auth: setup-test-env
	@echo "运行认证测试..."
	python -m pytest tests/api/test_auth.py -v

test-knowledge: setup-test-env
	@echo "运行知识点相关测试..."
	python -m pytest \
		tests/api/test_knowledge_points.py \
		tests/unit/models/test_knowledge.py \
		tests/unit/services/test_knowledge_service.py \
		-v

# 代码质量检查
quality:
	@echo "检查代码质量..."
	@echo "检查代码格式..."
	black --check app/ tests/
	@echo "检查导入排序..."
	isort --check-only app/ tests/
	@echo "检查代码风格..."
	flake8 app/ tests/
	@echo "检查类型注解..."
	mypy app/

# 格式化代码
format:
	@echo "格式化代码..."
	black app/ tests/
	isort app/ tests/
	@echo "代码格式化完成"

# 安全检查
security:
	@echo "运行安全检查..."
	bandit -r app/
	safety check

# 性能测试
performance: setup-test-env
	@echo "运行性能测试..."
	@echo "启动应用..."
	uvicorn app.main:app --host 0.0.0.0 --port 8000 &
	@sleep 10
	@echo "运行性能测试..."
	locust -f tests/performance/locustfile.py --headless -u 10 -r 2 -t 30s --host http://localhost:8000 || true
	@echo "停止应用..."
	@pkill -f "uvicorn app.main:app" || true

# 清理测试文件
clean:
	@echo "清理测试文件..."
	@if [ -f test.db ]; then rm test.db; fi
	@if [ -f .coverage ]; then rm .coverage; fi
	@if [ -d htmlcov ]; then rm -rf htmlcov; fi
	@if [ -d .pytest_cache ]; then rm -rf .pytest_cache; fi
	@find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	@find . -type f -name "*.pyc" -delete 2>/dev/null || true
	@echo "清理完成"

# 生成测试报告
report: setup-test-env
	@echo "生成测试报告..."
	python -m pytest tests/ \
		--cov=app \
		--cov-report=html \
		--cov-report=xml \
		--cov-report=term-missing \
		--junitxml=test-results.xml \
		-v
	@echo "测试报告生成完成:"
	@echo "- HTML覆盖率报告: htmlcov/index.html"
	@echo "- XML覆盖率报告: coverage.xml"
	@echo "- JUnit测试结果: test-results.xml"

# 持续集成测试
ci: quality test-coverage security
	@echo "持续集成测试完成"

# 开发环境测试
dev: format test-fast
	@echo "开发环境测试完成"

# 完整测试套件
full: clean install quality test-coverage security
	@echo "完整测试套件执行完成"
