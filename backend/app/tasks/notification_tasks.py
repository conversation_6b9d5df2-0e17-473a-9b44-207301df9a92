"""
通知相关的定时任务
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import List

from sqlalchemy.orm import Session
from app.core.database import get_db
from app.services.notification_service import NotificationService
from app.models.annotation import AnnotationTask, TaskStatus

logger = logging.getLogger(__name__)


class NotificationTasks:
    """通知任务类"""
    
    def __init__(self):
        self.notification_service = None
    
    async def check_deadline_reminders(self) -> int:
        """检查并发送截止时间提醒"""
        try:
            db = next(get_db())
            notification_service = NotificationService(db)
            
            sent_count = notification_service.check_and_send_deadline_reminders()
            
            if sent_count > 0:
                logger.info(f"发送了 {sent_count} 个截止时间提醒")
            
            return sent_count
            
        except Exception as e:
            logger.error(f"检查截止时间提醒失败: {e}")
            return 0
        finally:
            db.close()
    
    async def check_overdue_tasks(self) -> int:
        """检查并处理超期任务"""
        try:
            db = next(get_db())
            notification_service = NotificationService(db)
            
            # 发送超期通知
            sent_count = notification_service.check_and_send_overdue_notifications()
            
            # 自动处理超期任务
            overdue_count = await self._handle_overdue_tasks(db)
            
            if sent_count > 0:
                logger.info(f"发送了 {sent_count} 个超期通知")
            
            if overdue_count > 0:
                logger.info(f"处理了 {overdue_count} 个超期任务")
            
            return sent_count + overdue_count
            
        except Exception as e:
            logger.error(f"检查超期任务失败: {e}")
            return 0
        finally:
            db.close()
    
    async def _handle_overdue_tasks(self, db: Session) -> int:
        """处理超期任务"""
        try:
            # 查找超期任务
            overdue_tasks = db.query(AnnotationTask).filter(
                AnnotationTask.due_date < datetime.now(),
                AnnotationTask.status.in_([
                    TaskStatus.PENDING.value, 
                    TaskStatus.IN_PROGRESS.value
                ])
            ).all()
            
            handled_count = 0
            
            for task in overdue_tasks:
                # 根据业务规则处理超期任务
                if task.status == TaskStatus.PENDING.value:
                    # 待处理的任务可以重新分配
                    await self._reassign_overdue_task(db, task)
                elif task.status == TaskStatus.IN_PROGRESS.value:
                    # 进行中的任务给予宽限期
                    await self._extend_task_deadline(db, task)
                
                handled_count += 1
            
            if handled_count > 0:
                db.commit()
            
            return handled_count
            
        except Exception as e:
            logger.error(f"处理超期任务失败: {e}")
            db.rollback()
            return 0
    
    async def _reassign_overdue_task(self, db: Session, task: AnnotationTask):
        """重新分配超期任务"""
        try:
            # 记录原分配人员
            original_assignee = task.assigned_to
            
            # 清除分配，等待重新分配
            task.assigned_to = None
            task.updated_at = datetime.now()
            
            # 记录日志
            from app.models.annotation import AnnotationLog
            log = AnnotationLog(
                task_id=task.task_id,
                user_id=1,  # 系统用户
                action="auto_reassign",
                comment=f"任务超期，从用户 {original_assignee} 重新分配"
            )
            db.add(log)
            
            logger.info(f"任务 {task.task_id} 因超期被重新分配")
            
        except Exception as e:
            logger.error(f"重新分配任务失败: {e}")
    
    async def _extend_task_deadline(self, db: Session, task: AnnotationTask):
        """延长任务截止时间"""
        try:
            # 延长3天
            if task.due_date:
                task.due_date = task.due_date + timedelta(days=3)
                task.updated_at = datetime.now()
                
                # 记录日志
                from app.models.annotation import AnnotationLog
                log = AnnotationLog(
                    task_id=task.task_id,
                    user_id=1,  # 系统用户
                    action="auto_extend_deadline",
                    comment="任务超期，自动延长3天截止时间"
                )
                db.add(log)
                
                logger.info(f"任务 {task.task_id} 截止时间已延长至 {task.due_date}")
            
        except Exception as e:
            logger.error(f"延长任务截止时间失败: {e}")
    
    async def send_daily_summary(self) -> bool:
        """发送每日工作总结"""
        try:
            db = next(get_db())
            
            # 获取今日统计数据
            today = datetime.now().date()
            today_start = datetime.combine(today, datetime.min.time())
            today_end = datetime.combine(today, datetime.max.time())
            
            # 统计今日任务情况
            total_tasks = db.query(AnnotationTask).count()
            today_completed = db.query(AnnotationTask).filter(
                AnnotationTask.completed_at >= today_start,
                AnnotationTask.completed_at <= today_end,
                AnnotationTask.status == TaskStatus.COMPLETED.value
            ).count()
            
            today_created = db.query(AnnotationTask).filter(
                AnnotationTask.created_at >= today_start,
                AnnotationTask.created_at <= today_end
            ).count()
            
            pending_tasks = db.query(AnnotationTask).filter(
                AnnotationTask.status == TaskStatus.PENDING.value
            ).count()
            
            in_progress_tasks = db.query(AnnotationTask).filter(
                AnnotationTask.status == TaskStatus.IN_PROGRESS.value
            ).count()
            
            # 构建总结数据
            summary_data = {
                'date': today.isoformat(),
                'total_tasks': total_tasks,
                'today_completed': today_completed,
                'today_created': today_created,
                'pending_tasks': pending_tasks,
                'in_progress_tasks': in_progress_tasks,
                'completion_rate': (today_completed / today_created * 100) if today_created > 0 else 0
            }
            
            # 发送总结（这里可以集成邮件、消息等）
            logger.info(f"每日工作总结: {summary_data}")
            
            return True
            
        except Exception as e:
            logger.error(f"发送每日总结失败: {e}")
            return False
        finally:
            db.close()
    
    async def cleanup_old_notifications(self, days: int = 30) -> int:
        """清理旧的通知记录"""
        try:
            # 这里可以实现清理逻辑
            # 例如删除30天前的通知记录
            cutoff_date = datetime.now() - timedelta(days=days)
            
            # 实际实现需要根据通知存储方式来定
            cleaned_count = 0
            
            logger.info(f"清理了 {cleaned_count} 条旧通知记录")
            return cleaned_count
            
        except Exception as e:
            logger.error(f"清理旧通知记录失败: {e}")
            return 0


# 定时任务调度器
class TaskScheduler:
    """任务调度器"""
    
    def __init__(self):
        self.notification_tasks = NotificationTasks()
        self.running = False
    
    async def start(self):
        """启动定时任务"""
        self.running = True
        logger.info("定时任务调度器已启动")
        
        # 创建并发任务
        tasks = [
            self._schedule_deadline_reminders(),
            self._schedule_overdue_checks(),
            self._schedule_daily_summary(),
            self._schedule_cleanup()
        ]
        
        await asyncio.gather(*tasks)
    
    async def stop(self):
        """停止定时任务"""
        self.running = False
        logger.info("定时任务调度器已停止")
    
    async def _schedule_deadline_reminders(self):
        """调度截止时间提醒任务 - 每小时执行一次"""
        while self.running:
            try:
                await self.notification_tasks.check_deadline_reminders()
                await asyncio.sleep(3600)  # 1小时
            except Exception as e:
                logger.error(f"截止时间提醒任务异常: {e}")
                await asyncio.sleep(300)  # 出错后5分钟重试
    
    async def _schedule_overdue_checks(self):
        """调度超期检查任务 - 每6小时执行一次"""
        while self.running:
            try:
                await self.notification_tasks.check_overdue_tasks()
                await asyncio.sleep(21600)  # 6小时
            except Exception as e:
                logger.error(f"超期检查任务异常: {e}")
                await asyncio.sleep(1800)  # 出错后30分钟重试
    
    async def _schedule_daily_summary(self):
        """调度每日总结任务 - 每天晚上9点执行"""
        while self.running:
            try:
                now = datetime.now()
                # 计算到晚上9点的时间
                target_time = now.replace(hour=21, minute=0, second=0, microsecond=0)
                if target_time <= now:
                    target_time += timedelta(days=1)
                
                sleep_seconds = (target_time - now).total_seconds()
                await asyncio.sleep(sleep_seconds)
                
                if self.running:
                    await self.notification_tasks.send_daily_summary()
                
            except Exception as e:
                logger.error(f"每日总结任务异常: {e}")
                await asyncio.sleep(3600)  # 出错后1小时重试
    
    async def _schedule_cleanup(self):
        """调度清理任务 - 每周执行一次"""
        while self.running:
            try:
                await self.notification_tasks.cleanup_old_notifications()
                await asyncio.sleep(604800)  # 7天
            except Exception as e:
                logger.error(f"清理任务异常: {e}")
                await asyncio.sleep(86400)  # 出错后1天重试


# 全局调度器实例
scheduler = TaskScheduler()
