"""
知识本体模块数据模型
"""

from enum import Enum
from typing import Optional, List

from sqlalchemy import (
    Column, String, Boolean, Integer, SmallInteger, Text, 
    Index, CheckConstraint, ForeignKey, REAL, BigInteger
)
from sqlalchemy import Text
from sqlalchemy.orm import relationship

from .base import Base, TimestampMixin, UserTrackingMixin


class PrerequisiteSource(int, Enum):
    """先修关系来源枚举"""
    EXPERT = 0      # 专家标注
    ALGORITHM = 1   # 算法推断
    IMPORT = 2      # 数据导入


class KnowledgePoint(Base, TimestampMixin, UserTrackingMixin):
    """知识点表"""
    
    __tablename__ = "knowledge_points"
    
    # 主键
    kp_id = Column(
        Integer,
        primary_key=True,
        autoincrement=True,
        comment="知识点ID"
    )
    
    # 层级结构
    parent_id = Column(
        Integer,
        ForeignKey("knowledge_points.kp_id"),
        nullable=True,
        comment="父级知识点ID"
    )
    
    path = Column(
        Text,
        nullable=False,
        comment="知识点层级路径"
    )
    
    # 基本信息
    name = Column(
        Text,
        nullable=False,
        comment="知识点名称"
    )
    
    code = Column(
        Text,
        unique=True,
        nullable=False,
        comment="知识点编码"
    )
    
    description = Column(
        Text,
        nullable=True,
        comment="知识点描述"
    )
    
    # 属性
    is_leaf = Column(
        Boolean,
        default=False,
        nullable=False,
        comment="是否叶子节点"
    )
    
    difficulty_level = Column(
        SmallInteger,
        nullable=True,
        comment="难度等级(1-5)"
    )
    
    # 约束和索引
    __table_args__ = (
        CheckConstraint(
            "difficulty_level IS NULL OR (difficulty_level >= 1 AND difficulty_level <= 5)",
            name="ck_kp_difficulty_level"
        ),
        Index("idx_kp_parent_id", parent_id),
        Index("idx_kp_code", code),
        Index("idx_kp_path", path, postgresql_using="gist"),
        Index("idx_kp_is_leaf", is_leaf),
        Index("idx_kp_name", name, postgresql_using="gin", postgresql_ops={"name": "gin_trgm_ops"}),
        {"comment": "知识点表"}
    )
    
    # 关系
    parent = relationship("KnowledgePoint", remote_side=[kp_id], back_populates="children")
    children = relationship("KnowledgePoint", back_populates="parent", cascade="all, delete-orphan")
    
    # 先修关系
    prerequisites = relationship(
        "PrerequisiteRelation",
        foreign_keys="PrerequisiteRelation.post_kp_id",
        back_populates="post_kp",
        cascade="all, delete-orphan"
    )
    
    dependents = relationship(
        "PrerequisiteRelation",
        foreign_keys="PrerequisiteRelation.pre_kp_id",
        back_populates="pre_kp",
        cascade="all, delete-orphan"
    )

    @property
    def creator_id(self) -> Optional[int]:
        """creator_id属性作为created_by的别名，用于兼容性"""
        return self.created_by

    def __repr__(self) -> str:
        return f"<KnowledgePoint(id={self.kp_id}, name='{self.name}', code='{self.code}')>"


class PrerequisiteRelation(Base, TimestampMixin, UserTrackingMixin):
    """知识点先修关系表"""
    
    __tablename__ = "prerequisite_relation"
    
    # 复合主键
    pre_kp_id = Column(
        Integer,
        ForeignKey("knowledge_points.kp_id"),
        primary_key=True,
        comment="前置知识点ID"
    )
    
    post_kp_id = Column(
        Integer,
        ForeignKey("knowledge_points.kp_id"),
        primary_key=True,
        comment="后置知识点ID"
    )
    
    # 关系属性
    source = Column(
        SmallInteger,
        nullable=False,
        default=PrerequisiteSource.EXPERT.value,
        comment="关系来源"
    )
    
    confidence = Column(
        REAL,
        nullable=False,
        default=1.0,
        comment="置信度"
    )
    
    # 约束和索引
    __table_args__ = (
        CheckConstraint(
            "pre_kp_id != post_kp_id",
            name="ck_prereq_no_self_reference"
        ),
        CheckConstraint(
            source.in_([s.value for s in PrerequisiteSource]),
            name="ck_prereq_source"
        ),
        CheckConstraint(
            "confidence >= 0 AND confidence <= 1",
            name="ck_prereq_confidence"
        ),
        Index("idx_prereq_pre", pre_kp_id),
        Index("idx_prereq_post", post_kp_id),
        Index("idx_prereq_source", source),
        {"comment": "知识点先修关系表"}
    )
    
    # 关系
    pre_kp = relationship(
        "KnowledgePoint",
        foreign_keys=[pre_kp_id],
        back_populates="dependents"
    )
    
    post_kp = relationship(
        "KnowledgePoint",
        foreign_keys=[post_kp_id],
        back_populates="prerequisites"
    )

    @property
    def relation_type(self) -> int:
        """relation_type属性作为source的别名，用于兼容性"""
        return self.source

    @relation_type.setter
    def relation_type(self, value: int):
        """设置relation_type"""
        self.source = value

    @property
    def strength(self) -> float:
        """strength属性作为confidence的别名，用于兼容性"""
        return self.confidence

    @strength.setter
    def strength(self, value: float):
        """设置strength"""
        self.confidence = value

    def __repr__(self) -> str:
        return f"<PrerequisiteRelation(pre={self.pre_kp_id}, post={self.post_kp_id}, source={self.source})>"
