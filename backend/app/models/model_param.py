"""
模型参数模块数据模型
"""

from datetime import datetime
from enum import Enum
from typing import Optional

from sqlalchemy import (
    Column, String, Boolean, Integer, SmallInteger, Text,
    Index, CheckConstraint, ForeignKey, REAL, DateTime,
    Numeric, JSON
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship

from .base import Base, TimestampMixin, UserTrackingMixin


class ModelType(str, Enum):
    """模型类型枚举"""
    BKT = "BKT"           # 贝叶斯知识追踪
    IRT = "IRT"           # 项目反应理论
    DKT = "DKT"           # 深度知识追踪
    DINA = "DINA"         # 确定性输入噪声与门模型
    DINO = "DINO"         # 确定性输入噪声或门模型


class SkillParam(Base, TimestampMixin, UserTrackingMixin):
    """技能参数表(BKT参数)"""
    
    __tablename__ = "skill_param"
    
    # 主键(外键)
    kp_id = Column(
        Integer,
        ForeignKey("knowledge_points.kp_id"),
        primary_key=True,
        comment="知识点ID"
    )
    
    # BKT参数
    p_l0 = Column(
        REAL,
        nullable=True,
        comment="初始掌握概率"
    )
    
    p_t = Column(
        REAL,
        nullable=True,
        comment="学习率(转移概率)"
    )
    
    p_g = Column(
        REAL,
        nullable=True,
        comment="猜对概率"
    )
    
    p_s = Column(
        REAL,
        nullable=True,
        comment="滑落概率"
    )
    
    # 模型信息
    model_version = Column(
        String(16),
        nullable=True,
        comment="模型版本"
    )
    
    model_type = Column(
        String(10),
        nullable=False,
        default=ModelType.BKT.value,
        comment="模型类型"
    )
    
    # 校准信息
    last_update = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="最后更新时间"
    )
    
    calibration_quality = Column(
        Numeric(3, 2),
        nullable=True,
        comment="校准质量评分"
    )
    
    sample_size = Column(
        Integer,
        nullable=True,
        comment="校准样本量"
    )
    
    # 扩展参数
    extended_params = Column(
        JSON,
        nullable=True,
        comment="扩展参数(JSON格式)"
    )
    
    # 约束和索引
    __table_args__ = (
        CheckConstraint(
            "p_l0 IS NULL OR (p_l0 >= 0 AND p_l0 <= 1)",
            name="ck_skill_param_p_l0"
        ),
        CheckConstraint(
            "p_t IS NULL OR (p_t >= 0 AND p_t <= 1)",
            name="ck_skill_param_p_t"
        ),
        CheckConstraint(
            "p_g IS NULL OR (p_g >= 0 AND p_g <= 1)",
            name="ck_skill_param_p_g"
        ),
        CheckConstraint(
            "p_s IS NULL OR (p_s >= 0 AND p_s <= 1)",
            name="ck_skill_param_p_s"
        ),
        CheckConstraint(
            model_type.in_([t.value for t in ModelType]),
            name="ck_skill_param_model_type"
        ),
        CheckConstraint(
            "calibration_quality IS NULL OR (calibration_quality >= 0 AND calibration_quality <= 1)",
            name="ck_skill_param_calibration_quality"
        ),
        CheckConstraint(
            "sample_size IS NULL OR sample_size > 0",
            name="ck_skill_param_sample_size"
        ),
        Index("idx_skill_param_model_type", model_type),
        Index("idx_skill_param_last_update", last_update),
        Index("idx_skill_param_calibration_quality", calibration_quality),
        {"comment": "技能参数表(BKT参数)"}
    )
    
    # 关系
    knowledge_point = relationship("KnowledgePoint")
    
    def __repr__(self) -> str:
        return f"<SkillParam(kp_id={self.kp_id}, p_l0={self.p_l0}, p_t={self.p_t}, p_g={self.p_g}, p_s={self.p_s})>"


class ModelConfig(Base, TimestampMixin, UserTrackingMixin):
    """模型配置表"""
    
    __tablename__ = "model_config"
    
    # 主键
    config_id = Column(
        Integer,
        primary_key=True,
        autoincrement=True,
        comment="配置ID"
    )
    
    # 配置基本信息
    name = Column(
        String(100),
        nullable=False,
        unique=True,
        comment="配置名称"
    )
    
    description = Column(
        Text,
        nullable=True,
        comment="配置描述"
    )
    
    model_type = Column(
        String(10),
        nullable=False,
        comment="模型类型"
    )
    
    # 配置参数
    config_data = Column(
        JSON,
        nullable=False,
        comment="配置参数(JSON格式)"
    )
    
    # 状态信息
    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        comment="是否激活"
    )
    
    is_default = Column(
        Boolean,
        default=False,
        nullable=False,
        comment="是否默认配置"
    )
    
    # 版本信息
    version = Column(
        String(20),
        nullable=False,
        default="1.0.0",
        comment="配置版本"
    )
    
    # 约束和索引
    __table_args__ = (
        CheckConstraint(
            model_type.in_([t.value for t in ModelType]),
            name="ck_model_config_model_type"
        ),
        Index("idx_model_config_name", name),
        Index("idx_model_config_model_type", model_type),
        Index("idx_model_config_is_active", is_active),
        Index("idx_model_config_is_default", is_default),
        {"comment": "模型配置表"}
    )
    
    def __repr__(self) -> str:
        return f"<ModelConfig(id={self.config_id}, name='{self.name}', type='{self.model_type}')>"
