"""
版本管理模块数据模型
"""

from enum import Enum
from typing import Optional, Dict, Any

from sqlalchemy import (
    Column, String, Boolean, Integer, SmallInteger, Text,
    Index, CheckConstraint, ForeignKey, BigInteger, JSON
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship

from .base import Base, TimestampMixin, UserTrackingMixin


class ChangeType(int, Enum):
    """变更类型枚举"""
    CREATE = 0      # 创建
    UPDATE = 1      # 更新
    DELETE = 2      # 删除
    RESTORE = 3     # 恢复


class QuestionVersion(Base, TimestampMixin, UserTrackingMixin):
    """题目版本表"""
    
    __tablename__ = "question_versions"
    
    # 主键
    version_id = Column(
        BigInteger,
        primary_key=True,
        autoincrement=True,
        comment="版本ID"
    )
    
    # 外键
    question_id = Column(
        BigInteger,
        ForeignKey("questions.question_id"),
        nullable=False,
        comment="题目ID"
    )
    
    # 版本信息
    version_number = Column(
        Integer,
        nullable=False,
        comment="版本号"
    )
    
    version_tag = Column(
        String(50),
        nullable=True,
        comment="版本标签"
    )
    
    # 变更信息
    change_type = Column(
        SmallInteger,
        nullable=False,
        comment="变更类型"
    )
    
    change_description = Column(
        Text,
        nullable=True,
        comment="变更描述"
    )
    
    # 快照数据
    snapshot_data = Column(
        JSON,
        nullable=False,
        comment="题目快照数据"
    )

    # 差异数据
    diff_data = Column(
        JSON,
        nullable=True,
        comment="与前一版本的差异"
    )
    
    # 状态信息
    is_current = Column(
        Boolean,
        default=False,
        nullable=False,
        comment="是否当前版本"
    )
    
    is_published = Column(
        Boolean,
        default=False,
        nullable=False,
        comment="是否已发布"
    )
    
    # 元数据
    meta_data = Column(
        JSON,
        nullable=True,
        comment="版本元数据"
    )
    
    # 约束和索引
    __table_args__ = (
        CheckConstraint(
            change_type.in_([t.value for t in ChangeType]),
            name="ck_question_versions_change_type"
        ),
        CheckConstraint(
            "version_number > 0",
            name="ck_question_versions_version_number"
        ),
        Index("idx_question_versions_question_id", question_id),
        Index("idx_question_versions_version_number", version_number),
        Index("idx_question_versions_is_current", is_current),
        Index("idx_question_versions_is_published", is_published),
        Index("idx_question_versions_change_type", change_type),
        Index("idx_question_versions_composite", question_id, version_number),
        {"comment": "题目版本表"}
    )
    
    # 关系
    question = relationship("Question")
    
    def __repr__(self) -> str:
        return f"<QuestionVersion(id={self.version_id}, question_id={self.question_id}, version={self.version_number})>"


class KpVersion(Base, TimestampMixin, UserTrackingMixin):
    """知识点版本表"""
    
    __tablename__ = "kp_versions"
    
    # 主键
    version_id = Column(
        BigInteger,
        primary_key=True,
        autoincrement=True,
        comment="版本ID"
    )
    
    # 外键
    kp_id = Column(
        Integer,
        ForeignKey("knowledge_points.kp_id"),
        nullable=False,
        comment="知识点ID"
    )
    
    # 版本信息
    version_number = Column(
        Integer,
        nullable=False,
        comment="版本号"
    )
    
    version_tag = Column(
        String(50),
        nullable=True,
        comment="版本标签"
    )
    
    # 变更信息
    change_type = Column(
        SmallInteger,
        nullable=False,
        comment="变更类型"
    )
    
    change_description = Column(
        Text,
        nullable=True,
        comment="变更描述"
    )
    
    # 快照数据
    snapshot_data = Column(
        JSON,
        nullable=False,
        comment="知识点快照数据"
    )

    # 差异数据
    diff_data = Column(
        JSON,
        nullable=True,
        comment="与前一版本的差异"
    )
    
    # 状态信息
    is_current = Column(
        Boolean,
        default=False,
        nullable=False,
        comment="是否当前版本"
    )
    
    is_published = Column(
        Boolean,
        default=False,
        nullable=False,
        comment="是否已发布"
    )
    
    # 元数据
    meta_data = Column(
        JSON,
        nullable=True,
        comment="版本元数据"
    )
    
    # 约束和索引
    __table_args__ = (
        CheckConstraint(
            change_type.in_([t.value for t in ChangeType]),
            name="ck_kp_versions_change_type"
        ),
        CheckConstraint(
            "version_number > 0",
            name="ck_kp_versions_version_number"
        ),
        Index("idx_kp_versions_kp_id", kp_id),
        Index("idx_kp_versions_version_number", version_number),
        Index("idx_kp_versions_is_current", is_current),
        Index("idx_kp_versions_is_published", is_published),
        Index("idx_kp_versions_change_type", change_type),
        Index("idx_kp_versions_composite", kp_id, version_number),
        {"comment": "知识点版本表"}
    )
    
    # 关系
    knowledge_point = relationship("KnowledgePoint")
    
    def __repr__(self) -> str:
        return f"<KpVersion(id={self.version_id}, kp_id={self.kp_id}, version={self.version_number})>"
