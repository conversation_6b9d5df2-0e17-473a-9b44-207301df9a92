"""
知识空间模块数据模型
"""

from enum import Enum
from typing import Optional, List

from sqlalchemy import (
    Column, Boolean, Integer, SmallInteger, Text,
    Index, CheckConstraint, ForeignKey, REAL, BigInteger,
    ARRAY, JSON
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship

from .base import Base, TimestampMixin, UserTrackingMixin


class StateType(int, Enum):
    """知识状态类型枚举"""
    INITIAL = 0      # 初始状态
    INTERMEDIATE = 1 # 中间状态
    FINAL = 2        # 最终状态
    INVALID = 3      # 无效状态


class TransitionType(int, Enum):
    """状态转移类型枚举"""
    LEARNING = 0     # 学习转移
    FORGETTING = 1   # 遗忘转移
    INFERENCE = 2    # 推理转移


class KnowledgeState(Base, TimestampMixin, UserTrackingMixin):
    """知识状态表"""
    
    __tablename__ = "knowledge_state"
    
    # 主键
    state_id = Column(
        BigInteger,
        primary_key=True,
        autoincrement=True,
        comment="状态ID"
    )
    
    # 状态表示
    state_vector = Column(
        JSON,
        nullable=False,
        comment="知识状态向量(JSON格式)"
    )
    
    state_hash = Column(
        Text,
        nullable=False,
        unique=True,
        comment="状态哈希值"
    )
    
    # 状态属性
    state_type = Column(
        SmallInteger,
        nullable=False,
        default=StateType.INTERMEDIATE.value,
        comment="状态类型"
    )
    
    is_valid = Column(
        Boolean,
        nullable=False,
        default=True,
        comment="是否有效状态"
    )
    
    # 统计信息
    mastery_count = Column(
        Integer,
        nullable=False,
        default=0,
        comment="掌握的知识点数量"
    )
    
    total_count = Column(
        Integer,
        nullable=False,
        comment="总知识点数量"
    )
    
    mastery_ratio = Column(
        REAL,
        nullable=False,
        default=0.0,
        comment="掌握比例"
    )
    
    # 元数据
    description = Column(
        Text,
        nullable=True,
        comment="状态描述"
    )
    
    meta_data = Column(
        JSON,
        nullable=True,
        comment="元数据(JSON格式)"
    )
    
    # 约束和索引
    __table_args__ = (
        CheckConstraint(
            state_type.in_([t.value for t in StateType]),
            name="ck_knowledge_state_state_type"
        ),
        CheckConstraint(
            "mastery_count >= 0",
            name="ck_knowledge_state_mastery_count"
        ),
        CheckConstraint(
            "total_count > 0",
            name="ck_knowledge_state_total_count"
        ),
        CheckConstraint(
            "mastery_count <= total_count",
            name="ck_knowledge_state_mastery_le_total"
        ),
        CheckConstraint(
            "mastery_ratio >= 0 AND mastery_ratio <= 1",
            name="ck_knowledge_state_mastery_ratio"
        ),
        Index("idx_knowledge_state_state_hash", state_hash),
        Index("idx_knowledge_state_state_type", state_type),
        Index("idx_knowledge_state_is_valid", is_valid),
        Index("idx_knowledge_state_mastery_ratio", mastery_ratio),
        Index("idx_knowledge_state_mastery_count", mastery_count),
        {"comment": "知识状态表"}
    )
    
    # 关系
    outgoing_transitions = relationship(
        "StateTransition",
        foreign_keys="StateTransition.from_state_id",
        back_populates="from_state",
        cascade="all, delete-orphan"
    )
    
    incoming_transitions = relationship(
        "StateTransition",
        foreign_keys="StateTransition.to_state_id", 
        back_populates="to_state",
        cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        return f"<KnowledgeState(id={self.state_id}, type={self.state_type}, mastery={self.mastery_count}/{self.total_count})>"


class StateTransition(Base, TimestampMixin, UserTrackingMixin):
    """状态转移表"""
    
    __tablename__ = "state_transition"
    
    # 主键
    transition_id = Column(
        BigInteger,
        primary_key=True,
        autoincrement=True,
        comment="转移ID"
    )
    
    # 外键
    from_state_id = Column(
        BigInteger,
        ForeignKey("knowledge_state.state_id"),
        nullable=False,
        comment="源状态ID"
    )
    
    to_state_id = Column(
        BigInteger,
        ForeignKey("knowledge_state.state_id"),
        nullable=False,
        comment="目标状态ID"
    )
    
    # 转移属性
    transition_type = Column(
        SmallInteger,
        nullable=False,
        default=TransitionType.LEARNING.value,
        comment="转移类型"
    )
    
    probability = Column(
        REAL,
        nullable=False,
        default=1.0,
        comment="转移概率"
    )
    
    # 触发条件
    trigger_kp_id = Column(
        Integer,
        ForeignKey("knowledge_points.kp_id"),
        nullable=True,
        comment="触发知识点ID"
    )
    
    trigger_question_id = Column(
        BigInteger,
        ForeignKey("questions.question_id"),
        nullable=True,
        comment="触发题目ID"
    )
    
    # 转移成本
    learning_cost = Column(
        REAL,
        nullable=True,
        comment="学习成本"
    )
    
    difficulty = Column(
        REAL,
        nullable=True,
        comment="转移难度"
    )
    
    # 元数据
    description = Column(
        Text,
        nullable=True,
        comment="转移描述"
    )
    
    meta_data = Column(
        JSON,
        nullable=True,
        comment="元数据(JSON格式)"
    )
    
    # 约束和索引
    __table_args__ = (
        CheckConstraint(
            "from_state_id != to_state_id",
            name="ck_state_transition_no_self_loop"
        ),
        CheckConstraint(
            transition_type.in_([t.value for t in TransitionType]),
            name="ck_state_transition_transition_type"
        ),
        CheckConstraint(
            "probability >= 0 AND probability <= 1",
            name="ck_state_transition_probability"
        ),
        CheckConstraint(
            "learning_cost IS NULL OR learning_cost >= 0",
            name="ck_state_transition_learning_cost"
        ),
        CheckConstraint(
            "difficulty IS NULL OR (difficulty >= 0 AND difficulty <= 1)",
            name="ck_state_transition_difficulty"
        ),
        Index("idx_state_transition_from_state_id", from_state_id),
        Index("idx_state_transition_to_state_id", to_state_id),
        Index("idx_state_transition_transition_type", transition_type),
        Index("idx_state_transition_trigger_kp_id", trigger_kp_id),
        Index("idx_state_transition_trigger_question_id", trigger_question_id),
        Index("idx_state_transition_probability", probability),
        Index("idx_state_transition_composite", from_state_id, to_state_id, transition_type),
        {"comment": "状态转移表"}
    )
    
    # 关系
    from_state = relationship(
        "KnowledgeState",
        foreign_keys=[from_state_id],
        back_populates="outgoing_transitions"
    )
    
    to_state = relationship(
        "KnowledgeState",
        foreign_keys=[to_state_id],
        back_populates="incoming_transitions"
    )
    
    trigger_kp = relationship("KnowledgePoint")
    trigger_question = relationship("Question")
    
    def __repr__(self) -> str:
        return f"<StateTransition(id={self.transition_id}, from={self.from_state_id}, to={self.to_state_id}, type={self.transition_type})>"
