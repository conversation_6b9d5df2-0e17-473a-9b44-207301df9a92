"""
基础模型类和混入类
"""

from datetime import datetime
from typing import Any

from sqlalchemy import Column, DateTime, BigInteger, func, Boolean, text, Integer
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column


class Base(DeclarativeBase):
    """基础模型类"""
    pass


class TimestampMixin:
    """时间戳混入类"""

    @declared_attr
    def created_at(cls) -> Mapped[datetime]:
        return mapped_column(
            DateTime,
            server_default=text("CURRENT_TIMESTAMP"),
            nullable=False,
            comment="创建时间"
        )

    @declared_attr
    def updated_at(cls) -> Mapped[datetime]:
        return mapped_column(
            DateTime,
            server_default=text("CURRENT_TIMESTAMP"),
            onupdate=func.now(),
            nullable=False,
            comment="更新时间"
        )


class UserTrackingMixin:
    """用户追踪混入类"""

    @declared_attr
    def created_by(cls) -> Mapped[int]:
        return mapped_column(
            Integer,
            nullable=True,
            comment="创建用户ID"
        )

    @declared_attr
    def updated_by(cls) -> Mapped[int]:
        return mapped_column(
            Integer,
            nullable=True,
            comment="更新用户ID"
        )


class SoftDeleteMixin:
    """软删除混入类"""

    @declared_attr
    def is_deleted(cls) -> Mapped[bool]:
        return mapped_column(
            Boolean,
            default=False,
            nullable=False,
            comment="是否已删除"
        )

    @declared_attr
    def deleted_at(cls) -> Mapped[datetime]:
        return mapped_column(
            DateTime(timezone=True),
            nullable=True,
            comment="删除时间"
        )

    @declared_attr
    def deleted_by(cls) -> Mapped[int]:
        return mapped_column(
            BigInteger,
            nullable=True,
            comment="删除用户ID"
        )


def to_dict(obj: Any) -> dict:
    """将模型对象转换为字典"""
    if obj is None:
        return {}
    
    result = {}
    for column in obj.__table__.columns:
        value = getattr(obj, column.name)
        if isinstance(value, datetime):
            value = value.isoformat()
        result[column.name] = value
    
    return result
