"""
认证API端点测试
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.core.security import create_access_token, create_refresh_token, get_password_hash
from tests.utils.factories import UserFactory
from tests.utils.helpers import assert_response_success, assert_response_error


@pytest.mark.api
@pytest.mark.db
class TestAuthAPI:
    """认证API测试类"""
    
    def test_login_success(self, client: TestClient, db: Session):
        """测试成功登录"""
        UserFactory._meta.sqlalchemy_session = db
        
        # 创建测试用户
        password = "testpassword123"
        user = UserFactory(
            username="testuser",
            password_hash=get_password_hash(password),
            is_active=True
        )
        
        # 登录请求
        response = client.post(
            "/api/v1/auth/login",
            data={
                "username": "testuser",
                "password": password
            }
        )
        
        assert_response_success(response, 200)
        data = response.json()
        
        assert "access_token" in data
        assert "token_type" in data
        assert data["token_type"] == "bearer"
        assert "refresh_token" in data
        assert "expires_in" in data
    
    def test_login_invalid_username(self, client: TestClient, db: Session):
        """测试无效用户名登录"""
        response = client.post(
            "/api/v1/auth/login",
            data={
                "username": "nonexistent",
                "password": "password123"
            }
        )
        
        assert_response_error(response, 401, "用户名或密码错误")
    
    def test_login_invalid_password(self, client: TestClient, db: Session):
        """测试无效密码登录"""
        UserFactory._meta.sqlalchemy_session = db
        
        user = UserFactory(
            username="testuser",
            password_hash=get_password_hash("correctpassword"),
            is_active=True
        )
        
        response = client.post(
            "/api/v1/auth/login",
            data={
                "username": "testuser",
                "password": "wrongpassword"
            }
        )
        
        assert_response_error(response, 401, "用户名或密码错误")
    
    def test_login_inactive_user(self, client: TestClient, db: Session):
        """测试非活跃用户登录"""
        UserFactory._meta.sqlalchemy_session = db
        
        password = "testpassword123"
        user = UserFactory(
            username="inactiveuser",
            password_hash=get_password_hash(password),
            is_active=False
        )
        
        response = client.post(
            "/api/v1/auth/login",
            data={
                "username": "inactiveuser",
                "password": password
            }
        )
        
        assert_response_error(response, 401, "账户已被停用")
    
    def test_register_success(self, client: TestClient, db: Session):
        """测试成功注册"""
        user_data = {
            "username": "newuser",
            "email": "<EMAIL>",
            "password": "Password123",
            "full_name": "New User"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        
        assert_response_success(response, 201)
        data = response.json()
        
        assert data["username"] == "newuser"
        assert data["email"] == "<EMAIL>"
        assert data["full_name"] == "New User"
        assert data["is_active"] is True
        assert "password_hash" not in data
    
    def test_register_duplicate_username(self, client: TestClient, db: Session):
        """测试注册重复用户名"""
        UserFactory._meta.sqlalchemy_session = db
        existing_user = UserFactory(username="duplicate")
        
        user_data = {
            "username": "duplicate",
            "email": "<EMAIL>",
            "password": "Password123"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        
        assert_response_error(response, 400, "用户名已存在")
    
    def test_register_duplicate_email(self, client: TestClient, db: Session):
        """测试注册重复邮箱"""
        UserFactory._meta.sqlalchemy_session = db
        existing_user = UserFactory(email="<EMAIL>")
        
        user_data = {
            "username": "newuser",
            "email": "<EMAIL>",
            "password": "Password123"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        
        assert_response_error(response, 400, "邮箱已存在")
    
    def test_register_invalid_data(self, client: TestClient, db: Session):
        """测试注册无效数据"""
        user_data = {
            "username": "",  # 空用户名
            "email": "invalid-email",  # 无效邮箱
            "password": "123"  # 密码太短
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == 422  # 验证错误
    
    def test_get_current_user(self, client: TestClient, db: Session):
        """测试获取当前用户信息"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory()
        
        # 创建访问令牌
        access_token = create_access_token(subject=str(user.user_id))
        headers = {"Authorization": f"Bearer {access_token}"}

        response = client.get("/api/v1/auth/me", headers=headers)

        assert_response_success(response, 200)
        data = response.json()

        assert data["user_id"] == user.user_id
        assert data["username"] == user.username
        assert data["email"] == user.email
        assert "password_hash" not in data
    
    def test_get_current_user_invalid_token(self, client: TestClient, db: Session):
        """测试无效令牌获取当前用户"""
        headers = {"Authorization": "Bearer invalid_token"}
        
        response = client.get("/api/v1/auth/me", headers=headers)
        
        assert_response_error(response, 401, "无效的认证凭据")
    
    def test_get_current_user_no_token(self, client: TestClient, db: Session):
        """测试无令牌获取当前用户"""
        response = client.get("/api/v1/auth/me")
        
        assert_response_error(response, 401, "未提供认证凭据")
    
    def test_refresh_token(self, client: TestClient, db: Session):
        """测试刷新令牌"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory()
        
        # 创建刷新令牌
        refresh_token = create_refresh_token(subject=user.user_id)

        response = client.post("/api/v1/auth/refresh", json={"refresh_token": refresh_token})
        
        assert_response_success(response, 200)
        data = response.json()
        
        assert "access_token" in data
        assert "token_type" in data
        assert data["token_type"] == "bearer"
        assert data["access_token"] != access_token  # 新令牌应该不同
    
    def test_logout(self, client: TestClient, db: Session):
        """测试登出"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory()
        
        # 创建访问令牌
        access_token = create_access_token(subject=str(user.user_id))
        headers = {"Authorization": f"Bearer {access_token}"}
        
        response = client.post("/api/v1/auth/logout", headers=headers)
        
        assert_response_success(response, 200)
        data = response.json()
        
        assert data["message"] == "成功登出"
    
    def test_change_password(self, client: TestClient, db: Session):
        """测试修改密码"""
        UserFactory._meta.sqlalchemy_session = db
        
        old_password = "OldPassword123"
        user = UserFactory(
            password_hash=get_password_hash(old_password)
        )
        
        # 创建访问令牌
        access_token = create_access_token(subject=str(user.user_id))
        headers = {"Authorization": f"Bearer {access_token}"}
        
        password_data = {
            "old_password": old_password,
            "new_password": "NewPassword123"
        }
        
        response = client.post(
            "/api/v1/auth/change-password",
            json=password_data,
            headers=headers
        )
        
        assert_response_success(response, 200)
        data = response.json()
        
        assert data["message"] == "密码修改成功"
    
    def test_change_password_wrong_old_password(self, client: TestClient, db: Session):
        """测试修改密码时旧密码错误"""
        UserFactory._meta.sqlalchemy_session = db
        
        user = UserFactory(
            password_hash=get_password_hash("correctpassword")
        )
        
        # 创建访问令牌
        access_token = create_access_token(subject=str(user.user_id))
        headers = {"Authorization": f"Bearer {access_token}"}
        
        password_data = {
            "old_password": "wrongpassword",
            "new_password": "NewPassword123"
        }
        
        response = client.post(
            "/api/v1/auth/change-password",
            json=password_data,
            headers=headers
        )
        
        assert_response_error(response, 400, "旧密码错误")
    
    def test_forgot_password(self, client: TestClient, db: Session):
        """测试忘记密码"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory(email="<EMAIL>")
        
        response = client.post(
            "/api/v1/auth/forgot-password",
            json={"email": "<EMAIL>"}
        )
        
        assert_response_success(response, 200)
        data = response.json()
        
        assert data["message"] == "密码重置邮件已发送"
    
    def test_forgot_password_nonexistent_email(self, client: TestClient, db: Session):
        """测试忘记密码时邮箱不存在"""
        response = client.post(
            "/api/v1/auth/forgot-password",
            json={"email": "<EMAIL>"}
        )
        
        assert_response_error(response, 404, "邮箱不存在")
    
    def test_verify_email(self, client: TestClient, db: Session):
        """测试邮箱验证"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory()

        # 创建验证令牌
        verification_token = create_access_token(
            subject=str(user.user_id),
            expires_delta=None
        )

        response = client.post(
            f"/api/v1/auth/verify-email?token={verification_token}"
        )

        assert_response_success(response, 200)
        data = response.json()

        assert data["message"] == "邮箱验证成功"
