"""
用户管理API端点测试
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from tests.utils.factories import UserFactory, AdminUserFactory
from tests.utils.helpers import (
    create_auth_headers,
    assert_response_success,
    assert_response_error,
    assert_pagination_response
)


@pytest.mark.api
@pytest.mark.db
class TestUsersAPI:
    """用户管理API测试类"""
    
    def test_get_users_list_as_admin(self, client: TestClient, db: Session):
        """测试管理员获取用户列表"""
        UserFactory._meta.sqlalchemy_session = db
        AdminUserFactory._meta.sqlalchemy_session = db
        
        # 创建测试数据
        admin = AdminUserFactory()
        users = [UserFactory() for _ in range(5)]
        
        headers = create_auth_headers(admin)
        
        response = client.get("/api/v1/users/", headers=headers)
        
        assert_response_success(response, 200)
        data = response.json()
        
        assert_pagination_response(data, expected_total=6)  # 5个用户 + 1个管理员
        assert len(data["items"]) == 6
    
    def test_get_users_list_as_regular_user(self, client: TestClient, db: Session):
        """测试普通用户获取用户列表（应该被拒绝）"""
        UserFactory._meta.sqlalchemy_session = db
        
        user = UserFactory(role="annotator")
        headers = create_auth_headers(user)
        
        response = client.get("/api/v1/users/", headers=headers)
        
        # 根据权限设置，普通用户可能无法访问用户列表
        assert response.status_code in [403, 401]
    
    def test_get_users_with_pagination(self, client: TestClient, db: Session):
        """测试分页获取用户"""
        UserFactory._meta.sqlalchemy_session = db
        AdminUserFactory._meta.sqlalchemy_session = db
        
        admin = AdminUserFactory()
        users = [UserFactory() for _ in range(10)]
        
        headers = create_auth_headers(admin)
        
        response = client.get(
            "/api/v1/users/?skip=0&limit=5",
            headers=headers
        )
        
        assert_response_success(response, 200)
        data = response.json()
        
        assert data["total"] == 11  # 10个用户 + 1个管理员
        assert len(data["items"]) == 5
        assert data["skip"] == 0
        assert data["limit"] == 5
    
    def test_get_users_with_search(self, client: TestClient, db: Session):
        """测试搜索用户"""
        UserFactory._meta.sqlalchemy_session = db
        AdminUserFactory._meta.sqlalchemy_session = db
        
        admin = AdminUserFactory()
        
        # 创建特定用户名的用户
        user1 = UserFactory(username="alice_smith", full_name="Alice Smith")
        user2 = UserFactory(username="bob_jones", full_name="Bob Jones")
        user3 = UserFactory(username="alice_brown", full_name="Alice Brown")
        
        headers = create_auth_headers(admin)
        
        response = client.get(
            "/api/v1/users/?search=alice",
            headers=headers
        )
        
        assert_response_success(response, 200)
        data = response.json()
        
        assert data["total"] == 2
        usernames = [item["username"] for item in data["items"]]
        assert "alice_smith" in usernames
        assert "alice_brown" in usernames
        assert "bob_jones" not in usernames
    
    def test_get_users_with_role_filter(self, client: TestClient, db: Session):
        """测试按角色过滤用户"""
        UserFactory._meta.sqlalchemy_session = db
        AdminUserFactory._meta.sqlalchemy_session = db
        
        admin = AdminUserFactory()
        
        # 创建不同角色的用户
        annotator = UserFactory(role="annotator")
        reviewer = UserFactory(role="reviewer")
        
        headers = create_auth_headers(admin)
        
        response = client.get(
            "/api/v1/users/?role=annotator",
            headers=headers
        )
        
        assert_response_success(response, 200)
        data = response.json()
        
        assert data["total"] == 1
        assert data["items"][0]["role"] == "annotator"
    
    def test_get_user_by_id(self, client: TestClient, db: Session):
        """测试根据ID获取用户"""
        UserFactory._meta.sqlalchemy_session = db
        AdminUserFactory._meta.sqlalchemy_session = db
        
        admin = AdminUserFactory()
        user = UserFactory(username="testuser")
        
        headers = create_auth_headers(admin)
        
        response = client.get(f"/api/v1/users/{user.user_id}", headers=headers)

        assert_response_success(response, 200)
        data = response.json()

        assert data["user_id"] == user.user_id
        assert data["username"] == "testuser"
        assert "password_hash" not in data
    
    def test_get_user_not_found(self, client: TestClient, db: Session):
        """测试获取不存在的用户"""
        AdminUserFactory._meta.sqlalchemy_session = db
        admin = AdminUserFactory()
        
        headers = create_auth_headers(admin)
        
        response = client.get("/api/v1/users/99999", headers=headers)
        
        assert_response_error(response, 404, "用户不存在")
    
    def test_create_user_as_admin(self, client: TestClient, db: Session):
        """测试管理员创建用户"""
        AdminUserFactory._meta.sqlalchemy_session = db
        admin = AdminUserFactory()
        
        user_data = {
            "username": "newuser",
            "email": "<EMAIL>",
            "password": "password123",
            "full_name": "New User",
            "role": "annotator"
        }
        
        headers = create_auth_headers(admin)
        
        response = client.post(
            "/api/v1/users/",
            json=user_data,
            headers=headers
        )
        
        assert_response_success(response, 201)
        data = response.json()
        
        assert data["username"] == "newuser"
        assert data["email"] == "<EMAIL>"
        assert data["role"] == "annotator"
        assert "password_hash" not in data
    
    def test_create_user_duplicate_username(self, client: TestClient, db: Session):
        """测试创建重复用户名的用户"""
        UserFactory._meta.sqlalchemy_session = db
        AdminUserFactory._meta.sqlalchemy_session = db
        
        admin = AdminUserFactory()
        existing_user = UserFactory(username="duplicate")
        
        user_data = {
            "username": "duplicate",
            "email": "<EMAIL>",
            "password": "password123"
        }
        
        headers = create_auth_headers(admin)
        
        response = client.post(
            "/api/v1/users/",
            json=user_data,
            headers=headers
        )
        
        assert_response_error(response, 400, "用户名已存在")
    
    def test_update_user(self, client: TestClient, db: Session):
        """测试更新用户"""
        UserFactory._meta.sqlalchemy_session = db
        AdminUserFactory._meta.sqlalchemy_session = db
        
        admin = AdminUserFactory()
        user = UserFactory(full_name="Original Name")
        
        update_data = {
            "full_name": "Updated Name",
            "role": "reviewer"
        }
        
        headers = create_auth_headers(admin)
        
        response = client.put(
            f"/api/v1/users/{user.user_id}",
            json=update_data,
            headers=headers
        )
        
        assert_response_success(response, 200)
        data = response.json()
        
        assert data["full_name"] == "Updated Name"
        assert data["role"] == "reviewer"
    
    def test_update_user_not_found(self, client: TestClient, db: Session):
        """测试更新不存在的用户"""
        AdminUserFactory._meta.sqlalchemy_session = db
        admin = AdminUserFactory()
        
        update_data = {"full_name": "New Name"}
        headers = create_auth_headers(admin)
        
        response = client.put(
            "/api/v1/users/99999",
            json=update_data,
            headers=headers
        )
        
        assert_response_error(response, 404, "用户不存在")
    
    def test_delete_user(self, client: TestClient, db: Session):
        """测试删除用户"""
        UserFactory._meta.sqlalchemy_session = db
        AdminUserFactory._meta.sqlalchemy_session = db
        
        admin = AdminUserFactory()
        user = UserFactory()
        
        headers = create_auth_headers(admin)
        
        response = client.delete(f"/api/v1/users/{user.user_id}", headers=headers)
        
        assert_response_success(response, 200)
        data = response.json()
        
        assert data["message"] == "用户删除成功"
    
    def test_delete_user_not_found(self, client: TestClient, db: Session):
        """测试删除不存在的用户"""
        AdminUserFactory._meta.sqlalchemy_session = db
        admin = AdminUserFactory()
        
        headers = create_auth_headers(admin)
        
        response = client.delete("/api/v1/users/99999", headers=headers)
        
        assert_response_error(response, 404, "用户不存在")
    
    def test_activate_user(self, client: TestClient, db: Session):
        """测试激活用户"""
        UserFactory._meta.sqlalchemy_session = db
        AdminUserFactory._meta.sqlalchemy_session = db
        
        admin = AdminUserFactory()
        user = UserFactory(is_active=False)
        
        headers = create_auth_headers(admin)
        
        response = client.post(
            f"/api/v1/users/{user.user_id}/activate",
            headers=headers
        )
        
        assert_response_success(response, 200)
        data = response.json()
        
        assert data["is_active"] is True
        assert data["message"] == "用户已激活"
    
    def test_deactivate_user(self, client: TestClient, db: Session):
        """测试停用用户"""
        UserFactory._meta.sqlalchemy_session = db
        AdminUserFactory._meta.sqlalchemy_session = db
        
        admin = AdminUserFactory()
        user = UserFactory(is_active=True)
        
        headers = create_auth_headers(admin)
        
        response = client.post(
            f"/api/v1/users/{user.user_id}/deactivate",
            headers=headers
        )
        
        assert_response_success(response, 200)
        data = response.json()
        
        assert data["is_active"] is False
        assert data["message"] == "用户已停用"
    
    def test_change_user_password(self, client: TestClient, db: Session):
        """测试管理员修改用户密码"""
        UserFactory._meta.sqlalchemy_session = db
        AdminUserFactory._meta.sqlalchemy_session = db
        
        admin = AdminUserFactory()
        user = UserFactory()
        
        password_data = {"new_password": "newpassword123"}
        headers = create_auth_headers(admin)
        
        response = client.post(
            f"/api/v1/users/{user.user_id}/change-password",
            json=password_data,
            headers=headers
        )
        
        assert_response_success(response, 200)
        data = response.json()
        
        assert data["message"] == "密码修改成功"
    
    def test_get_user_statistics(self, client: TestClient, db: Session):
        """测试获取用户统计信息"""
        UserFactory._meta.sqlalchemy_session = db
        AdminUserFactory._meta.sqlalchemy_session = db
        
        admin = AdminUserFactory()
        
        # 创建不同类型的用户
        UserFactory(role="annotator", is_active=True)
        UserFactory(role="annotator", is_active=False)
        UserFactory(role="reviewer", is_active=True)
        
        headers = create_auth_headers(admin)
        
        response = client.get("/api/v1/users/statistics", headers=headers)
        
        assert_response_success(response, 200)
        data = response.json()
        
        assert "total" in data
        assert "active" in data
        assert "inactive" in data
        assert "by_role" in data
        assert data["total"] == 4  # 3个用户 + 1个管理员
        assert data["active"] == 3
        assert data["inactive"] == 1
    
    def test_get_current_user_profile(self, client: TestClient, db: Session):
        """测试获取当前用户资料"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory()
        
        headers = create_auth_headers(user)
        
        response = client.get("/api/v1/users/me", headers=headers)
        
        assert_response_success(response, 200)
        data = response.json()
        
        assert data["user_id"] == user.user_id
        assert data["username"] == user.username
        assert "password_hash" not in data
    
    def test_update_current_user_profile(self, client: TestClient, db: Session):
        """测试更新当前用户资料"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory(full_name="Original Name")
        
        update_data = {
            "full_name": "Updated Name",
            "email": "<EMAIL>"
        }
        
        headers = create_auth_headers(user)
        
        response = client.put(
            "/api/v1/users/me",
            json=update_data,
            headers=headers
        )
        
        assert_response_success(response, 200)
        data = response.json()
        
        assert data["full_name"] == "Updated Name"
        assert data["email"] == "<EMAIL>"
    
    def test_unauthorized_access(self, client: TestClient, db: Session):
        """测试未授权访问"""
        response = client.get("/api/v1/users/")
        
        assert_response_error(response, 401, "未提供认证凭据")
