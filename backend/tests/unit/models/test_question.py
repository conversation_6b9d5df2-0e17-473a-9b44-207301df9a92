"""
题目模型单元测试
"""

import pytest
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from app.models.question import Question, QuestionAsset, ItemParam
from tests.utils.factories import (
    QuestionFactory, 
    SingleChoiceQuestionFactory,
    UserFactory
)


@pytest.mark.unit
@pytest.mark.db
class TestQuestionModel:
    """题目模型测试类"""
    
    def test_create_question(self, db: Session):
        """测试创建题目"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory()
        
        question_data = {
            "content": {"title": "测试题目", "body": "这是一道测试题目的内容"},
            "q_type": 1,  # 单选题
            "difficulty_lvl": 3,
            "answer_key": {"correct": "A"},
            "analysis": "这是答案解析",
            "created_by": user.user_id,
            "updated_by": user.user_id
        }
        
        question = Question(**question_data)
        db.add(question)
        db.commit()
        db.refresh(question)
        
        assert question.question_id is not None
        assert question.content["title"] == "测试题目"
        assert question.content["body"] == "这是一道测试题目的内容"
        assert question.q_type == 1
        assert question.difficulty_lvl == 3
        assert question.answer_key["correct"] == "A"
        assert question.analysis == "这是答案解析"
        assert question.created_at is not None
        assert question.updated_at is not None
    
    def test_question_factory(self, db: Session):
        """测试题目工厂"""
        QuestionFactory._meta.sqlalchemy_session = db
        question = QuestionFactory()

        assert question.question_id is not None
        assert question.content is not None
        assert question.q_type in [1, 2, 3, 4]  # QuestionType枚举值
        assert 1 <= question.difficulty_lvl <= 5
        assert question.analysis is not None
    
    def test_single_choice_question_factory(self, db: Session):
        """测试单选题工厂"""
        SingleChoiceQuestionFactory._meta.sqlalchemy_session = db
        question = SingleChoiceQuestionFactory()

        assert question.q_type == 1  # SINGLE_CHOICE
        assert question.answer_key is not None
        assert question.content is not None
    
    def test_question_types(self, db: Session):
        """测试题目类型"""
        QuestionFactory._meta.sqlalchemy_session = db

        question_types = [0, 1, 2, 3, 4]  # QuestionType枚举值

        for qtype in question_types:
            question = QuestionFactory(q_type=qtype)
            assert question.q_type == qtype
    
    def test_question_difficulty_range(self, db: Session):
        """测试题目难度范围"""
        QuestionFactory._meta.sqlalchemy_session = db

        for difficulty in range(1, 6):
            question = QuestionFactory(difficulty_lvl=difficulty)
            assert question.difficulty_lvl == difficulty
    
    def test_question_status_values(self, db: Session):
        """测试题目状态值"""
        QuestionFactory._meta.sqlalchemy_session = db

        # Question模型没有status字段，跳过此测试或测试其他字段
        question = QuestionFactory()
        assert question.q_type is not None
    
    def test_question_repr(self, db: Session):
        """测试题目字符串表示"""
        QuestionFactory._meta.sqlalchemy_session = db
        question = QuestionFactory()

        repr_str = repr(question)
        assert str(question.question_id) in repr_str
    
    def test_question_relationships(self, db: Session):
        """测试题目关系"""
        QuestionFactory._meta.sqlalchemy_session = db
        question = QuestionFactory()
        
        # 测试题目可以访问相关的知识点、资产等
        assert hasattr(question, 'knowledge_points')
        assert hasattr(question, 'assets')
        assert hasattr(question, 'creator')
        assert hasattr(question, 'updater')
        assert hasattr(question, 'annotation_tasks')
    
    def test_question_options_json(self, db: Session):
        """测试题目选项JSON存储"""
        QuestionFactory._meta.sqlalchemy_session = db
        
        options = [
            {"key": "A", "content": "选项A"},
            {"key": "B", "content": "选项B"},
            {"key": "C", "content": "选项C"},
            {"key": "D", "content": "选项D"}
        ]
        
        question = QuestionFactory(
            question_type="single_choice",
            options=options
        )
        
        assert question.options == options
        assert isinstance(question.options, list)
        assert len(question.options) == 4
    
    def test_question_metadata_json(self, db: Session):
        """测试题目元数据JSON存储"""
        QuestionFactory._meta.sqlalchemy_session = db
        
        metadata = {
            "tags": ["代数", "方程"],
            "keywords": ["一元二次方程", "求解"],
            "estimated_time": 300,  # 秒
            "cognitive_level": "应用"
        }
        
        question = QuestionFactory(metadata=metadata)
        
        assert question.metadata == metadata
        assert isinstance(question.metadata, dict)
        assert question.metadata["tags"] == ["代数", "方程"]


@pytest.mark.unit
@pytest.mark.db
class TestQuestionAssetModel:
    """题目资产模型测试类"""
    
    def test_create_question_asset(self, db: Session):
        """测试创建题目资产"""
        QuestionFactory._meta.sqlalchemy_session = db
        UserFactory._meta.sqlalchemy_session = db
        
        question = QuestionFactory()
        user = UserFactory()
        
        asset_data = {
            "question_id": question.question_id,
            "asset_type": "image",
            "file_name": "test_image.png",
            "file_path": "/uploads/images/test_image.png",
            "file_size": 1024,
            "mime_type": "image/png",
            "description": "测试图片",
            "created_by": user.user_id
        }
        
        asset = QuestionAsset(**asset_data)
        db.add(asset)
        db.commit()
        db.refresh(asset)
        
        assert asset.asset_id is not None
        assert asset.question_id == question.question_id
        assert asset.asset_type == "image"
        assert asset.file_name == "test_image.png"
        assert asset.file_path == "/uploads/images/test_image.png"
        assert asset.file_size == 1024
        assert asset.mime_type == "image/png"
        assert asset.created_at is not None
    
    def test_question_asset_types(self, db: Session):
        """测试题目资产类型"""
        QuestionFactory._meta.sqlalchemy_session = db
        UserFactory._meta.sqlalchemy_session = db
        
        question = QuestionFactory()
        user = UserFactory()
        
        asset_types = ["image", "audio", "video", "document", "other"]
        
        for asset_type in asset_types:
            asset = QuestionAsset(
                question_id=question.question_id,
                asset_type=asset_type,
                file_name=f"test.{asset_type}",
                file_path=f"/uploads/{asset_type}/test.{asset_type}",
                created_by=user.user_id
            )
            db.add(asset)
        
        db.commit()
        
        assets = db.query(QuestionAsset).filter(
            QuestionAsset.question_id == question.question_id
        ).all()
        
        assert len(assets) == len(asset_types)
    
    def test_question_asset_relationship(self, db: Session):
        """测试题目资产关系"""
        QuestionFactory._meta.sqlalchemy_session = db
        UserFactory._meta.sqlalchemy_session = db
        
        question = QuestionFactory()
        user = UserFactory()
        
        asset = QuestionAsset(
            question_id=question.question_id,
            asset_type="image",
            file_name="test.png",
            file_path="/uploads/test.png",
            created_by=user.user_id
        )
        db.add(asset)
        db.commit()
        db.refresh(asset)
        
        # 测试关系
        assert asset.question == question
        assert asset in question.assets


@pytest.mark.unit
@pytest.mark.db
class TestItemParamModel:
    """题目参数模型测试类"""
    
    def test_create_item_param(self, db: Session):
        """测试创建题目参数"""
        QuestionFactory._meta.sqlalchemy_session = db
        UserFactory._meta.sqlalchemy_session = db
        
        question = QuestionFactory()
        user = UserFactory()
        
        param_data = {
            "question_id": question.question_id,
            "param_type": "irt",
            "param_name": "discrimination",
            "param_value": 1.2,
            "confidence": 0.85,
            "estimation_method": "mle",
            "created_by": user.user_id,
            "updated_by": user.user_id
        }
        
        param = ItemParam(**param_data)
        db.add(param)
        db.commit()
        db.refresh(param)
        
        assert param.param_id is not None
        assert param.question_id == question.question_id
        assert param.param_type == "irt"
        assert param.param_name == "discrimination"
        assert param.param_value == 1.2
        assert param.confidence == 0.85
        assert param.estimation_method == "mle"
        assert param.created_at is not None
        assert param.updated_at is not None
    
    def test_item_param_types(self, db: Session):
        """测试题目参数类型"""
        QuestionFactory._meta.sqlalchemy_session = db
        UserFactory._meta.sqlalchemy_session = db
        
        question = QuestionFactory()
        user = UserFactory()
        
        param_types = ["irt", "difficulty", "discrimination", "guessing", "cognitive"]
        
        for param_type in param_types:
            param = ItemParam(
                question_id=question.question_id,
                param_type=param_type,
                param_name="test_param",
                param_value=1.0,
                created_by=user.user_id,
                updated_by=user.user_id
            )
            db.add(param)
        
        db.commit()
        
        params = db.query(ItemParam).filter(
            ItemParam.question_id == question.question_id
        ).all()
        
        assert len(params) == len(param_types)
    
    def test_item_param_relationship(self, db: Session):
        """测试题目参数关系"""
        QuestionFactory._meta.sqlalchemy_session = db
        UserFactory._meta.sqlalchemy_session = db
        
        question = QuestionFactory()
        user = UserFactory()
        
        param = ItemParam(
            question_id=question.question_id,
            param_type="irt",
            param_name="difficulty",
            param_value=0.5,
            created_by=user.user_id,
            updated_by=user.user_id
        )
        db.add(param)
        db.commit()
        db.refresh(param)
        
        # 测试关系
        assert param.question == question
        assert param in question.parameters
