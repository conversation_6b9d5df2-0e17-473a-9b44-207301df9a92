"""
用户模型单元测试
"""

import pytest
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from app.models.user import User, UserSession
from tests.utils.factories import UserFactory, AdminUserFactory


@pytest.mark.unit
@pytest.mark.db
class TestUserModel:
    """用户模型测试类"""
    
    def test_create_user(self, db: Session):
        """测试创建用户"""
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "full_name": "Test User",
            "password_hash": "hashed_password",
            "role": "annotator"
        }
        
        user = User(**user_data)
        db.add(user)
        db.commit()
        db.refresh(user)
        
        assert user.user_id is not None
        assert user.username == "testuser"
        assert user.email == "<EMAIL>"
        assert user.full_name == "Test User"
        assert user.role == "annotator"
        assert user.is_active is True
        assert user.created_at is not None
        assert user.updated_at is not None
    
    def test_user_factory(self, db: Session):
        """测试用户工厂"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory()
        
        assert user.user_id is not None
        assert user.username is not None
        assert user.email is not None
        assert user.role == "annotator"
        assert user.is_active is True
    
    def test_admin_user_factory(self, db: Session):
        """测试管理员用户工厂"""
        AdminUserFactory._meta.sqlalchemy_session = db
        admin = AdminUserFactory()
        
        assert admin.user_id is not None
        assert admin.role == "admin"
        assert admin.username.startswith("admin")
    
    def test_username_unique_constraint(self, db: Session):
        """测试用户名唯一约束"""
        UserFactory._meta.sqlalchemy_session = db
        
        # 创建第一个用户
        user1 = UserFactory(username="duplicate_user")
        
        # 尝试创建相同用户名的用户
        with pytest.raises(IntegrityError):
            user2 = UserFactory(username="duplicate_user")
            db.commit()
    
    def test_email_unique_constraint(self, db: Session):
        """测试邮箱唯一约束"""
        UserFactory._meta.sqlalchemy_session = db
        
        # 创建第一个用户
        user1 = UserFactory(email="<EMAIL>")
        
        # 尝试创建相同邮箱的用户
        with pytest.raises(IntegrityError):
            user2 = UserFactory(email="<EMAIL>")
            db.commit()
    
    def test_user_repr(self, db: Session):
        """测试用户字符串表示"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory(username="testuser")
        
        repr_str = repr(user)
        assert "testuser" in repr_str
        assert str(user.user_id) in repr_str
    
    def test_user_role_validation(self, db: Session):
        """测试用户角色验证"""
        valid_roles = ["admin", "annotator", "reviewer", "viewer"]
        
        for role in valid_roles:
            user = User(
                username=f"user_{role}",
                email=f"{role}@example.com",
                password_hash="password",
                role=role
            )
            db.add(user)
        
        db.commit()
        
        # 验证所有用户都被成功创建
        users = db.query(User).all()
        assert len(users) == len(valid_roles)
    
    def test_user_default_values(self, db: Session):
        """测试用户默认值"""
        user = User(
            username="defaultuser",
            email="<EMAIL>",
            password_hash="password"
        )
        db.add(user)
        db.commit()
        db.refresh(user)
        
        assert user.is_active is True
        assert user.role == "annotator"  # 默认角色
    
    def test_user_timestamps(self, db: Session):
        """测试用户时间戳"""
        import time

        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory()

        original_updated_at = user.updated_at

        # 添加小延迟确保时间戳不同
        time.sleep(0.1)

        # 更新用户
        user.full_name = "Updated Name"
        db.commit()
        db.refresh(user)

        # 由于SQLite的时间戳精度问题，我们检查字段是否被更新
        assert user.full_name == "Updated Name"
        assert user.updated_at is not None
    
    def test_user_relationships(self, db: Session):
        """测试用户关系"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory()

        # 测试用户可以访问相关的会话
        assert hasattr(user, 'sessions')
        assert user.sessions == []  # 新用户没有会话


@pytest.mark.unit
@pytest.mark.db
class TestUserSessionModel:
    """用户会话模型测试类"""
    
    def test_create_user_session(self, db: Session):
        """测试创建用户会话"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory()
        
        session_data = {
            "session_id": "test_session_id",
            "user_id": user.user_id,
            "ip_address": "***********",
            "user_agent": "Test User Agent",
            "expires_at": datetime.utcnow() + timedelta(hours=24),
            "is_active": True
        }
        
        session = UserSession(**session_data)
        db.add(session)
        db.commit()
        db.refresh(session)
        
        assert session.session_id is not None
        assert session.user_id == user.user_id
        assert session.session_id == "test_session_id"
        assert session.ip_address == "***********"
        assert session.is_active is True
        assert session.created_at is not None
    
    def test_user_session_relationship(self, db: Session):
        """测试用户会话关系"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory()
        
        session = UserSession(
            session_id="test_session_id",
            user_id=user.user_id,
            ip_address="127.0.0.1",
            expires_at=datetime.utcnow() + timedelta(hours=24)
        )
        db.add(session)
        db.commit()
        db.refresh(session)
        
        # 测试关系
        assert session.user == user
        assert session in user.sessions
    
    def test_session_id_unique_constraint(self, db: Session):
        """测试会话ID唯一约束"""
        UserFactory._meta.sqlalchemy_session = db
        user1 = UserFactory()
        user2 = UserFactory()

        # 创建第一个会话
        session1 = UserSession(
            session_id="unique_session_1",
            user_id=user1.user_id,
            ip_address="127.0.0.1",
            expires_at=datetime.utcnow() + timedelta(hours=24)
        )
        db.add(session1)
        db.commit()

        # 创建不同ID的会话应该成功
        session2 = UserSession(
            session_id="unique_session_2",
            user_id=user2.user_id,
            ip_address="*********",
            expires_at=datetime.utcnow() + timedelta(hours=24)
        )
        db.add(session2)
        db.commit()

        # 验证两个会话都存在
        sessions = db.query(UserSession).all()
        assert len(sessions) == 2
    
    def test_session_default_values(self, db: Session):
        """测试会话默认值"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory()

        session = UserSession(
            session_id="test_session",
            user_id=user.user_id,
            expires_at=datetime.utcnow() + timedelta(hours=24)
        )
        db.add(session)
        db.commit()
        db.refresh(session)

        assert session.is_active is True
        assert session.expires_at is not None
