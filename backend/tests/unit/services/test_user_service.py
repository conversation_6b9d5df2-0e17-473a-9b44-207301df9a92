"""
用户服务单元测试
"""

import pytest
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session

from app.services.user_service import UserService
from app.models.user import User
from app.schemas.user import UserCreate, UserUpdate
from app.core.security import verify_password, get_password_hash
from tests.utils.factories import UserFactory, AdminUserFactory


@pytest.mark.unit
@pytest.mark.db
class TestUserService:
    """用户服务测试类"""
    
    def test_get_user_by_id(self, db: Session):
        """测试根据ID获取用户"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory()
        
        service = UserService(db)
        result = service.get(user.user_id)
        
        assert result is not None
        assert result.id == user.user_id
        assert result.username == user.username
        assert result.email == user.email
    
    def test_get_user_by_username(self, db: Session):
        """测试根据用户名获取用户"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory(username="testuser123")
        
        service = UserService(db)
        result = service.get_by_username("testuser123")
        
        assert result is not None
        assert result.username == "testuser123"
        assert result.id == user.user_id
    
    def test_get_user_by_email(self, db: Session):
        """测试根据邮箱获取用户"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory(email="<EMAIL>")
        
        service = UserService(db)
        result = service.get_by_email("<EMAIL>")
        
        assert result is not None
        assert result.email == "<EMAIL>"
        assert result.id == user.user_id
    
    def test_get_user_not_found(self, db: Session):
        """测试获取不存在的用户"""
        service = UserService(db)
        
        assert service.get(99999) is None
        assert service.get_by_username("nonexistent") is None
        assert service.get_by_email("<EMAIL>") is None
    
    def test_get_users_list(self, db: Session):
        """测试获取用户列表"""
        UserFactory._meta.sqlalchemy_session = db
        
        # 创建多个用户
        users = [UserFactory() for _ in range(5)]
        
        service = UserService(db)
        result = service.get_multi(skip=0, limit=10)
        
        assert len(result) == 5
        assert all(isinstance(user, User) for user in result)
    
    def test_get_users_with_pagination(self, db: Session):
        """测试分页获取用户"""
        UserFactory._meta.sqlalchemy_session = db
        
        # 创建10个用户
        users = [UserFactory() for _ in range(10)]
        
        service = UserService(db)
        
        # 测试第一页
        page1 = service.get_multi(skip=0, limit=5)
        assert len(page1) == 5
        
        # 测试第二页
        page2 = service.get_multi(skip=5, limit=5)
        assert len(page2) == 5
        
        # 确保没有重复
        page1_ids = {user.user_id for user in page1}
        page2_ids = {user.user_id for user in page2}
        assert page1_ids.isdisjoint(page2_ids)
    
    def test_search_users(self, db: Session):
        """测试搜索用户"""
        UserFactory._meta.sqlalchemy_session = db
        
        # 创建特定用户名的用户
        user1 = UserFactory(username="alice_smith", full_name="Alice Smith")
        user2 = UserFactory(username="bob_jones", full_name="Bob Jones")
        user3 = UserFactory(username="alice_brown", full_name="Alice Brown")
        
        service = UserService(db)
        
        # 搜索包含"alice"的用户
        results = service.get_multi(search="alice")
        result_usernames = [user.username for user in results]
        
        assert "alice_smith" in result_usernames
        assert "alice_brown" in result_usernames
        assert "bob_jones" not in result_usernames
    
    def test_filter_users_by_role(self, db: Session):
        """测试按角色过滤用户"""
        UserFactory._meta.sqlalchemy_session = db
        AdminUserFactory._meta.sqlalchemy_session = db
        
        # 创建不同角色的用户
        annotator = UserFactory(role="annotator")
        reviewer = UserFactory(role="reviewer")
        admin = AdminUserFactory()
        
        service = UserService(db)
        
        # 过滤管理员用户
        admin_results = service.get_multi(filters={"role": "admin"})
        assert len(admin_results) == 1
        assert admin_results[0].role == "admin"
    
    def test_filter_users_by_status(self, db: Session):
        """测试按状态过滤用户"""
        UserFactory._meta.sqlalchemy_session = db
        
        # 创建不同状态的用户
        active_user = UserFactory(is_active=True)
        inactive_user = UserFactory(is_active=False)
        
        service = UserService(db)
        
        # 过滤活跃用户
        active_results = service.get_multi(filters={"is_active": True})
        assert len(active_results) == 1
        assert active_results[0].is_active is True
    
    def test_count_users(self, db: Session):
        """测试统计用户数量"""
        UserFactory._meta.sqlalchemy_session = db
        
        # 创建用户
        users = [UserFactory() for _ in range(7)]
        
        service = UserService(db)
        count = service.count()
        
        assert count == 7
    
    def test_create_user(self, db: Session):
        """测试创建用户"""
        user_data = UserCreate(
            username="newuser",
            email="<EMAIL>",
            password="password123",
            full_name="New User",
            role="annotator"
        )
        
        service = UserService(db)
        result = service.create(user_data)
        
        assert result.username == "newuser"
        assert result.email == "<EMAIL>"
        assert result.full_name == "New User"
        assert result.role == "annotator"
        assert result.is_active is True
        
        # 验证密码已被哈希
        assert result.password_hash != "password123"
        assert verify_password("password123", result.password_hash)
    
    def test_create_user_with_duplicate_username(self, db: Session):
        """测试创建重复用户名的用户"""
        UserFactory._meta.sqlalchemy_session = db
        existing_user = UserFactory(username="duplicate")
        
        user_data = UserCreate(
            username="duplicate",
            email="<EMAIL>",
            password="password123"
        )
        
        service = UserService(db)
        
        with pytest.raises(ValueError, match="用户名已存在"):
            service.create(user_data)
    
    def test_create_user_with_duplicate_email(self, db: Session):
        """测试创建重复邮箱的用户"""
        UserFactory._meta.sqlalchemy_session = db
        existing_user = UserFactory(email="<EMAIL>")
        
        user_data = UserCreate(
            username="newuser",
            email="<EMAIL>",
            password="password123"
        )
        
        service = UserService(db)
        
        with pytest.raises(ValueError, match="邮箱已存在"):
            service.create(user_data)
    
    def test_update_user(self, db: Session):
        """测试更新用户"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory(full_name="Original Name")
        
        update_data = UserUpdate(
            full_name="Updated Name",
            role="reviewer"
        )
        
        service = UserService(db)
        result = service.update(user.user_id, update_data)
        
        assert result.full_name == "Updated Name"
        assert result.role == "reviewer"
        assert result.username == user.username  # 未更新的字段保持不变
    
    def test_update_user_password(self, db: Session):
        """测试更新用户密码"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory()
        original_password_hash = user.password_hash

        service = UserService(db)
        result = service.update_password(user.user_id, "newpassword123")

        assert result.password_hash != original_password_hash
        assert verify_password("newpassword123", result.password_hash)
    
    def test_update_nonexistent_user(self, db: Session):
        """测试更新不存在的用户"""
        update_data = UserUpdate(full_name="New Name")
        
        service = UserService(db)
        result = service.update(99999, update_data)
        
        assert result is None
    
    def test_delete_user(self, db: Session):
        """测试删除用户"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory()
        user_id = user.user_id
        
        service = UserService(db)
        result = service.delete(user_id)
        
        assert result is True
        
        # 验证用户已被删除
        deleted_user = service.get(user_id)
        assert deleted_user is None
    
    def test_delete_nonexistent_user(self, db: Session):
        """测试删除不存在的用户"""
        service = UserService(db)
        result = service.delete(99999)
        
        assert result is False
    
    def test_authenticate_user(self, db: Session):
        """测试用户认证"""
        UserFactory._meta.sqlalchemy_session = db
        password = "testpassword123"
        hashed_password = get_password_hash(password)
        user = UserFactory(
            username="testuser",
            password_hash=hashed_password
        )
        
        service = UserService(db)
        
        # 正确的用户名和密码
        result = service.authenticate("testuser", password)
        assert result is not None
        assert result.username == "testuser"
        
        # 错误的密码
        result = service.authenticate("testuser", "wrongpassword")
        assert result is None
        
        # 不存在的用户
        result = service.authenticate("nonexistent", password)
        assert result is None
    
    def test_activate_user(self, db: Session):
        """测试激活用户"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory(is_active=False)
        
        service = UserService(db)
        result = service.activate(user.user_id)
        
        assert result.is_active is True
    
    def test_deactivate_user(self, db: Session):
        """测试停用用户"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory(is_active=True)
        
        service = UserService(db)
        result = service.deactivate(user.user_id)
        
        assert result.is_active is False
    
    def test_verify_user(self, db: Session):
        """测试验证用户"""
        UserFactory._meta.sqlalchemy_session = db
        user = UserFactory()

        service = UserService(db)
        result = service.verify(user.user_id)

        # 由于User模型没有is_verified字段，这里只验证方法调用成功
        assert result is not None
    
    def test_get_user_statistics(self, db: Session):
        """测试获取用户统计信息"""
        UserFactory._meta.sqlalchemy_session = db
        AdminUserFactory._meta.sqlalchemy_session = db
        
        # 创建不同类型的用户
        UserFactory(role="annotator", is_active=True)
        UserFactory(role="annotator", is_active=False)
        UserFactory(role="reviewer", is_active=True)
        AdminUserFactory(is_active=True)
        
        service = UserService(db)
        stats = service.get_statistics()
        
        assert stats["total"] == 4
        assert stats["active"] == 3
        assert stats["inactive"] == 1
        assert stats["by_role"]["annotator"] == 2
        assert stats["by_role"]["reviewer"] == 1
        assert stats["by_role"]["admin"] == 1
    
    def test_validate_user_data(self, db: Session):
        """测试用户数据验证"""
        service = UserService(db)
        
        # 测试有效数据
        valid_data = {
            "username": "validuser",
            "email": "<EMAIL>",
            "password": "validpassword123"
        }
        
        is_valid, errors = service.validate_user_data(valid_data)
        assert is_valid is True
        assert len(errors) == 0
        
        # 测试无效数据
        invalid_data = {
            "username": "",  # 空用户名
            "email": "invalid-email",  # 无效邮箱
            "password": "123"  # 密码太短
        }
        
        is_valid, errors = service.validate_user_data(invalid_data)
        assert is_valid is False
        assert len(errors) > 0
