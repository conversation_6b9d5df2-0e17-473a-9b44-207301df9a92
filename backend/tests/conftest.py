"""
Pytest配置文件
包含全局fixtures和测试配置
"""

import asyncio
import os
import pytest
from typing import Generator, AsyncGenerator
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from fastapi.testclient import TestClient
from httpx import AsyncClient

# 设置测试环境变量
os.environ["TESTING"] = "true"
os.environ["DATABASE_URL"] = "sqlite:///./test.db"

from app.main import app
from app.core.database import get_db, Base
# 导入所有模型以确保它们被注册到Base.metadata
from app.models import *  # noqa


# 测试数据库配置
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)

TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
def db() -> Generator[Session, None, None]:
    """
    创建测试数据库会话
    每个测试函数都会获得一个干净的数据库
    """
    # 创建所有表
    Base.metadata.create_all(bind=engine)
    
    # 创建数据库会话
    session = TestingSessionLocal()
    
    try:
        yield session
    finally:
        session.close()
        # 清理数据库
        Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def client(db: Session) -> Generator[TestClient, None, None]:
    """
    创建测试客户端
    """
    def override_get_db():
        try:
            yield db
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as test_client:
        yield test_client
    
    app.dependency_overrides.clear()


@pytest.fixture(scope="function")
async def async_client(db: Session) -> AsyncGenerator[AsyncClient, None]:
    """
    创建异步测试客户端
    """
    def override_get_db():
        try:
            yield db
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac
    
    app.dependency_overrides.clear()


@pytest.fixture
def test_user_data():
    """测试用户数据"""
    return {
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "testpassword123",
        "full_name": "Test User",
        "role": "annotator"
    }


@pytest.fixture
def test_knowledge_point_data():
    """测试知识点数据"""
    return {
        "name": "测试知识点",
        "code": "TEST_KP_001",
        "description": "这是一个测试知识点",
        "subject": "数学",
        "grade": "高中",
        "difficulty": 3,
        "is_leaf": True
    }


@pytest.fixture
def test_question_data():
    """测试题目数据"""
    return {
        "title": "测试题目",
        "content": "这是一道测试题目的内容",
        "question_type": "single_choice",
        "difficulty": 3,
        "subject": "数学",
        "grade": "高中",
        "options": [
            {"key": "A", "content": "选项A"},
            {"key": "B", "content": "选项B"},
            {"key": "C", "content": "选项C"},
            {"key": "D", "content": "选项D"}
        ],
        "answer": "A",
        "explanation": "这是答案解析"
    }


@pytest.fixture
def test_annotation_task_data():
    """测试标注任务数据"""
    return {
        "title": "测试标注任务",
        "description": "这是一个测试标注任务",
        "task_type": "knowledge_point_mapping",
        "priority": "medium",
        "deadline": "2024-12-31T23:59:59"
    }


# 标记配置
pytest_plugins = []

# 测试标记
def pytest_configure(config):
    """配置pytest标记"""
    config.addinivalue_line(
        "markers", "unit: 标记为单元测试"
    )
    config.addinivalue_line(
        "markers", "integration: 标记为集成测试"
    )
    config.addinivalue_line(
        "markers", "api: 标记为API测试"
    )
    config.addinivalue_line(
        "markers", "db: 标记为需要数据库的测试"
    )
    config.addinivalue_line(
        "markers", "slow: 标记为慢速测试"
    )
