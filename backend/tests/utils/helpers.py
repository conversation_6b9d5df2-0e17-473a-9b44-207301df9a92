"""
测试辅助工具函数
"""

import json
from typing import Dict, Any, Optional
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.orm import Session

from app.core.security import create_access_token
from app.models.user import User


def create_auth_headers(user: User) -> Dict[str, str]:
    """创建认证头"""
    access_token = create_access_token(subject=str(user.user_id))
    return {"Authorization": f"Bearer {access_token}"}


def login_user(client: TestClient, username: str, password: str) -> Dict[str, Any]:
    """用户登录并返回token"""
    response = client.post(
        "/api/v1/auth/login",
        data={"username": username, "password": password}
    )
    assert response.status_code == 200
    return response.json()


def create_authenticated_client(client: TestClient, user: User) -> TestClient:
    """创建已认证的客户端"""
    headers = create_auth_headers(user)
    client.headers.update(headers)
    return client


def assert_response_success(response, expected_status: int = 200):
    """断言响应成功"""
    assert response.status_code == expected_status, f"Expected {expected_status}, got {response.status_code}: {response.text}"


def assert_response_error(response, expected_status: int, expected_detail: Optional[str] = None):
    """断言响应错误"""
    assert response.status_code == expected_status
    if expected_detail:
        response_data = response.json()
        assert expected_detail in response_data.get("detail", "")


def assert_pagination_response(response_data: Dict[str, Any], expected_total: Optional[int] = None):
    """断言分页响应格式"""
    assert "items" in response_data
    assert "total" in response_data
    assert "skip" in response_data
    assert "limit" in response_data
    assert isinstance(response_data["items"], list)
    assert isinstance(response_data["total"], int)
    assert isinstance(response_data["skip"], int)
    assert isinstance(response_data["limit"], int)
    
    if expected_total is not None:
        assert response_data["total"] == expected_total


def assert_model_fields(model_data: Dict[str, Any], expected_fields: list[str]):
    """断言模型包含期望的字段"""
    for field in expected_fields:
        assert field in model_data, f"Field '{field}' not found in model data"


def assert_timestamp_fields(model_data: Dict[str, Any]):
    """断言时间戳字段存在且格式正确"""
    timestamp_fields = ["created_at", "updated_at"]
    for field in timestamp_fields:
        if field in model_data:
            assert model_data[field] is not None
            # 可以添加更多时间格式验证


def create_test_file_upload(filename: str = "test.txt", content: str = "test content") -> Dict[str, Any]:
    """创建测试文件上传"""
    return {
        "file": (filename, content, "text/plain")
    }


def compare_models(model1: Dict[str, Any], model2: Dict[str, Any], ignore_fields: list[str] = None):
    """比较两个模型数据"""
    ignore_fields = ignore_fields or ["id", "created_at", "updated_at"]
    
    for key, value in model1.items():
        if key not in ignore_fields:
            assert key in model2, f"Field '{key}' not found in second model"
            assert model2[key] == value, f"Field '{key}' values don't match: {value} != {model2[key]}"


def clean_db_session(db: Session):
    """清理数据库会话"""
    db.rollback()
    db.close()


def count_table_rows(db: Session, model_class) -> int:
    """统计表行数"""
    return db.query(model_class).count()


def get_model_by_id(db: Session, model_class, model_id: int):
    """根据ID获取模型"""
    return db.query(model_class).filter(model_class.id == model_id).first()


def create_mock_request_data(base_data: Dict[str, Any], **overrides) -> Dict[str, Any]:
    """创建模拟请求数据"""
    data = base_data.copy()
    data.update(overrides)
    return data


def extract_ids_from_list(items: list[Dict[str, Any]], id_field: str = "id") -> list[int]:
    """从列表中提取ID"""
    return [item[id_field] for item in items]


def sort_by_field(items: list[Dict[str, Any]], field: str, reverse: bool = False) -> list[Dict[str, Any]]:
    """按字段排序"""
    return sorted(items, key=lambda x: x[field], reverse=reverse)


def filter_by_field(items: list[Dict[str, Any]], field: str, value: Any) -> list[Dict[str, Any]]:
    """按字段过滤"""
    return [item for item in items if item.get(field) == value]


def validate_json_schema(data: Dict[str, Any], required_fields: list[str], optional_fields: list[str] = None):
    """验证JSON模式"""
    optional_fields = optional_fields or []
    
    # 检查必需字段
    for field in required_fields:
        assert field in data, f"Required field '{field}' is missing"
    
    # 检查是否有未知字段
    all_allowed_fields = set(required_fields + optional_fields)
    for field in data.keys():
        assert field in all_allowed_fields, f"Unknown field '{field}' found"


def create_test_search_params(search: str = None, skip: int = 0, limit: int = 10, **filters) -> Dict[str, Any]:
    """创建测试搜索参数"""
    params = {"skip": skip, "limit": limit}
    
    if search:
        params["search"] = search
    
    params.update(filters)
    return params
