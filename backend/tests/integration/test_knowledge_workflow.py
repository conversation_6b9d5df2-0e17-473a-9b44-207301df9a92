"""
知识点管理工作流集成测试
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from tests.utils.factories import UserFactory, AdminUserFactory
from tests.utils.helpers import (
    create_auth_headers,
    assert_response_success,
    assert_response_error
)


@pytest.mark.integration
@pytest.mark.db
class TestKnowledgeWorkflow:
    """知识点管理工作流集成测试"""
    
    def test_complete_knowledge_point_lifecycle(self, client: TestClient, db: Session):
        """测试完整的知识点生命周期"""
        AdminUserFactory._meta.sqlalchemy_session = db
        admin = AdminUserFactory()
        headers = create_auth_headers(admin)
        
        # 1. 创建根知识点
        root_data = {
            "name": "数学基础",
            "code": "MATH_ROOT",
            "description": "数学基础知识",
            "subject": "数学",
            "grade": "高中",
            "difficulty": 1
        }
        
        response = client.post(
            "/api/v1/knowledge-points/",
            json=root_data,
            headers=headers
        )
        assert_response_success(response, 201)
        root_kp = response.json()
        
        # 2. 创建子知识点
        child_data = {
            "name": "代数基础",
            "code": "ALGEBRA_BASIC",
            "description": "代数基础概念",
            "subject": "数学",
            "grade": "高中",
            "difficulty": 2,
            "parent_id": root_kp["kp_id"]
        }
        
        response = client.post(
            "/api/v1/knowledge-points/",
            json=child_data,
            headers=headers
        )
        assert_response_success(response, 201)
        child_kp = response.json()
        
        # 验证子知识点的路径
        assert child_kp["parent_id"] == root_kp["kp_id"]
        assert child_kp["path"] == f"{root_kp['path']}.{child_data['code']}"
        
        # 3. 获取知识点树形结构
        response = client.get(
            "/api/v1/knowledge-points/tree?subject=数学",
            headers=headers
        )
        assert_response_success(response, 200)
        tree_data = response.json()
        
        # 验证树形结构
        assert len(tree_data["tree"]) == 1
        root_node = tree_data["tree"][0]
        assert root_node["name"] == "数学基础"
        assert len(root_node["children"]) == 1
        assert root_node["children"][0]["name"] == "代数基础"
        
        # 4. 更新知识点
        update_data = {
            "description": "更新后的代数基础描述",
            "difficulty": 3
        }
        
        response = client.put(
            f"/api/v1/knowledge-points/{child_kp['kp_id']}",
            json=update_data,
            headers=headers
        )
        assert_response_success(response, 200)
        updated_kp = response.json()
        
        assert updated_kp["description"] == "更新后的代数基础描述"
        assert updated_kp["difficulty"] == 3
        
        # 5. 搜索知识点
        response = client.get(
            "/api/v1/knowledge-points/?search=代数",
            headers=headers
        )
        assert_response_success(response, 200)
        search_data = response.json()
        
        assert search_data["total"] == 1
        assert search_data["items"][0]["name"] == "代数基础"
        
        # 6. 删除子知识点
        response = client.delete(
            f"/api/v1/knowledge-points/{child_kp['kp_id']}",
            headers=headers
        )
        assert_response_success(response, 200)
        
        # 7. 验证删除后的状态
        response = client.get(
            f"/api/v1/knowledge-points/{child_kp['kp_id']}",
            headers=headers
        )
        assert_response_error(response, 404, "知识点不存在")
        
        # 8. 验证根知识点仍然存在
        response = client.get(
            f"/api/v1/knowledge-points/{root_kp['kp_id']}",
            headers=headers
        )
        assert_response_success(response, 200)
    
    def test_knowledge_point_hierarchy_management(self, client: TestClient, db: Session):
        """测试知识点层次结构管理"""
        AdminUserFactory._meta.sqlalchemy_session = db
        admin = AdminUserFactory()
        headers = create_auth_headers(admin)
        
        # 创建多层次知识点结构
        # 数学 -> 代数 -> 一元二次方程
        
        # 1. 创建根节点
        math_data = {
            "name": "数学",
            "code": "MATH",
            "subject": "数学",
            "grade": "高中"
        }
        
        response = client.post(
            "/api/v1/knowledge-points/",
            json=math_data,
            headers=headers
        )
        math_kp = response.json()
        
        # 2. 创建二级节点
        algebra_data = {
            "name": "代数",
            "code": "ALGEBRA",
            "subject": "数学",
            "grade": "高中",
            "parent_id": math_kp["kp_id"]
        }
        
        response = client.post(
            "/api/v1/knowledge-points/",
            json=algebra_data,
            headers=headers
        )
        algebra_kp = response.json()
        
        # 3. 创建三级节点
        equation_data = {
            "name": "一元二次方程",
            "code": "QUADRATIC_EQUATION",
            "subject": "数学",
            "grade": "高中",
            "parent_id": algebra_kp["kp_id"]
        }
        
        response = client.post(
            "/api/v1/knowledge-points/",
            json=equation_data,
            headers=headers
        )
        equation_kp = response.json()
        
        # 4. 验证路径生成
        assert equation_kp["path"] == "MATH.ALGEBRA.QUADRATIC_EQUATION"
        
        # 5. 获取完整树形结构
        response = client.get(
            "/api/v1/knowledge-points/tree",
            headers=headers
        )
        tree_data = response.json()
        
        # 验证三层结构
        root = tree_data["tree"][0]
        assert root["name"] == "数学"
        assert len(root["children"]) == 1
        
        level2 = root["children"][0]
        assert level2["name"] == "代数"
        assert len(level2["children"]) == 1
        
        level3 = level2["children"][0]
        assert level3["name"] == "一元二次方程"
        assert len(level3["children"]) == 0  # 叶子节点
    
    def test_knowledge_point_validation_workflow(self, client: TestClient, db: Session):
        """测试知识点验证工作流"""
        AdminUserFactory._meta.sqlalchemy_session = db
        admin = AdminUserFactory()
        headers = create_auth_headers(admin)
        
        # 1. 尝试创建无效的知识点
        invalid_data = {
            "name": "",  # 空名称
            "code": "",  # 空编码
            "subject": "数学"
        }
        
        response = client.post(
            "/api/v1/knowledge-points/",
            json=invalid_data,
            headers=headers
        )
        assert response.status_code == 422  # 验证错误
        
        # 2. 创建有效的知识点
        valid_data = {
            "name": "有效知识点",
            "code": "VALID_KP",
            "subject": "数学",
            "grade": "高中",
            "difficulty": 3
        }
        
        response = client.post(
            "/api/v1/knowledge-points/",
            json=valid_data,
            headers=headers
        )
        assert_response_success(response, 201)
        kp = response.json()
        
        # 3. 尝试创建重复编码的知识点
        duplicate_data = {
            "name": "重复编码知识点",
            "code": "VALID_KP",  # 重复编码
            "subject": "数学"
        }
        
        response = client.post(
            "/api/v1/knowledge-points/",
            json=duplicate_data,
            headers=headers
        )
        assert_response_error(response, 400)  # 应该返回错误
        
        # 4. 尝试创建无效父节点的知识点
        invalid_parent_data = {
            "name": "孤儿知识点",
            "code": "ORPHAN_KP",
            "parent_id": 99999,  # 不存在的父节点
            "subject": "数学"
        }
        
        response = client.post(
            "/api/v1/knowledge-points/",
            json=invalid_parent_data,
            headers=headers
        )
        assert_response_error(response, 400, "父知识点不存在")
    
    def test_knowledge_point_permissions_workflow(self, client: TestClient, db: Session):
        """测试知识点权限工作流"""
        UserFactory._meta.sqlalchemy_session = db
        AdminUserFactory._meta.sqlalchemy_session = db
        
        # 创建不同角色的用户
        admin = AdminUserFactory()
        annotator = UserFactory(role="annotator")
        viewer = UserFactory(role="viewer")
        
        # 管理员创建知识点
        admin_headers = create_auth_headers(admin)
        kp_data = {
            "name": "管理员创建的知识点",
            "code": "ADMIN_KP",
            "subject": "数学"
        }
        
        response = client.post(
            "/api/v1/knowledge-points/",
            json=kp_data,
            headers=admin_headers
        )
        assert_response_success(response, 201)
        kp = response.json()
        
        # 所有用户都可以查看知识点
        for user in [admin, annotator, viewer]:
            headers = create_auth_headers(user)
            response = client.get(
                f"/api/v1/knowledge-points/{kp['kp_id']}",
                headers=headers
            )
            assert_response_success(response, 200)
        
        # 只有管理员可以修改知识点
        update_data = {"description": "更新描述"}
        
        # 管理员可以修改
        response = client.put(
            f"/api/v1/knowledge-points/{kp['kp_id']}",
            json=update_data,
            headers=admin_headers
        )
        assert_response_success(response, 200)
        
        # 普通用户不能修改（根据实际权限设置）
        annotator_headers = create_auth_headers(annotator)
        response = client.put(
            f"/api/v1/knowledge-points/{kp['kp_id']}",
            json=update_data,
            headers=annotator_headers
        )
        # 根据权限设置，可能返回403或401
        assert response.status_code in [403, 401, 200]  # 根据实际实现调整
    
    def test_bulk_knowledge_point_operations(self, client: TestClient, db: Session):
        """测试批量知识点操作"""
        AdminUserFactory._meta.sqlalchemy_session = db
        admin = AdminUserFactory()
        headers = create_auth_headers(admin)
        
        # 1. 批量创建知识点
        knowledge_points = []
        for i in range(5):
            kp_data = {
                "name": f"批量知识点{i+1}",
                "code": f"BULK_KP_{i+1:03d}",
                "subject": "数学",
                "difficulty": (i % 5) + 1
            }
            
            response = client.post(
                "/api/v1/knowledge-points/",
                json=kp_data,
                headers=headers
            )
            assert_response_success(response, 201)
            knowledge_points.append(response.json())
        
        # 2. 验证所有知识点都被创建
        response = client.get(
            "/api/v1/knowledge-points/?limit=10",
            headers=headers
        )
        assert_response_success(response, 200)
        data = response.json()
        assert data["total"] >= 5
        
        # 3. 按难度过滤
        response = client.get(
            "/api/v1/knowledge-points/?difficulty=3",
            headers=headers
        )
        assert_response_success(response, 200)
        filtered_data = response.json()
        
        # 验证过滤结果
        for item in filtered_data["items"]:
            assert item["difficulty"] == 3
        
        # 4. 批量删除（如果支持）
        for kp in knowledge_points:
            response = client.delete(
                f"/api/v1/knowledge-points/{kp['kp_id']}",
                headers=headers
            )
            assert_response_success(response, 200)
