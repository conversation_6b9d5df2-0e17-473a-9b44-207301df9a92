"""
标注工作流集成测试
"""

import pytest
from datetime import datetime, timedelta
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from tests.utils.factories import (
    UserFactory,
    AdminUserFactory,
    QuestionFactory,
    KnowledgePointFactory
)
from tests.utils.helpers import (
    create_auth_headers,
    assert_response_success,
    assert_response_error
)


@pytest.mark.integration
@pytest.mark.db
class TestAnnotationWorkflow:
    """标注工作流集成测试"""
    
    def test_complete_annotation_task_lifecycle(self, client: TestClient, db: Session):
        """测试完整的标注任务生命周期"""
        UserFactory._meta.sqlalchemy_session = db
        AdminUserFactory._meta.sqlalchemy_session = db
        QuestionFactory._meta.sqlalchemy_session = db
        KnowledgePointFactory._meta.sqlalchemy_session = db
        
        # 创建用户
        admin = AdminUserFactory()
        annotator = UserFactory(role="annotator")
        reviewer = UserFactory(role="reviewer")
        
        # 创建测试数据
        question = QuestionFactory()
        kp1 = KnowledgePointFactory()
        kp2 = KnowledgePointFactory()
        
        admin_headers = create_auth_headers(admin)
        annotator_headers = create_auth_headers(annotator)
        reviewer_headers = create_auth_headers(reviewer)
        
        # 1. 管理员创建标注任务
        task_data = {
            "title": "知识点映射任务",
            "description": "为题目标注相关知识点",
            "task_type": "knowledge_point_mapping",
            "priority": "medium",
            "deadline": (datetime.utcnow() + timedelta(days=7)).isoformat(),
            "assigned_to": annotator.id,
            "question_ids": [question.question_id],
            "metadata": {
                "instructions": "请仔细分析题目内容，标注相关知识点",
                "quality_criteria": {"accuracy": 0.9}
            }
        }
        
        response = client.post(
            "/api/v1/annotation/tasks/",
            json=task_data,
            headers=admin_headers
        )
        assert_response_success(response, 201)
        task = response.json()
        
        assert task["title"] == "知识点映射任务"
        assert task["status"] == "pending"
        assert task["assigned_to"] == annotator.id
        
        # 2. 标注员查看分配给自己的任务
        response = client.get(
            "/api/v1/annotation/tasks/my-tasks",
            headers=annotator_headers
        )
        assert_response_success(response, 200)
        my_tasks = response.json()
        
        assert my_tasks["total"] >= 1
        task_ids = [t["task_id"] for t in my_tasks["items"]]
        assert task["task_id"] in task_ids
        
        # 3. 标注员开始任务
        response = client.post(
            f"/api/v1/annotation/tasks/{task['task_id']}/start",
            headers=annotator_headers
        )
        assert_response_success(response, 200)
        started_task = response.json()
        
        assert started_task["status"] == "in_progress"
        assert started_task["started_at"] is not None
        
        # 4. 标注员提交标注结果
        annotation_result = {
            "knowledge_points": [kp1.kp_id, kp2.kp_id],
            "relevance_scores": {
                str(kp1.kp_id): 0.9,
                str(kp2.kp_id): 0.7
            },
            "confidence": 0.85,
            "notes": "题目主要涉及这两个知识点"
        }
        
        response = client.post(
            f"/api/v1/annotation/tasks/{task['task_id']}/submit",
            json={"result": annotation_result, "notes": "标注完成"},
            headers=annotator_headers
        )
        assert_response_success(response, 200)
        submitted_task = response.json()
        
        assert submitted_task["status"] == "completed"
        assert submitted_task["completed_at"] is not None
        assert submitted_task["result"] == annotation_result
        
        # 5. 查看任务日志
        response = client.get(
            f"/api/v1/annotation/tasks/{task['task_id']}/logs",
            headers=admin_headers
        )
        assert_response_success(response, 200)
        logs = response.json()
        
        # 应该有开始和完成的日志
        log_actions = [log["action"] for log in logs]
        assert "start" in log_actions
        assert "complete" in log_actions
        
        # 6. 审核员审核任务（如果有审核流程）
        review_data = {
            "status": "approved",
            "quality_score": 0.9,
            "feedback": "标注质量良好"
        }
        
        response = client.post(
            f"/api/v1/annotation/tasks/{task['task_id']}/review",
            json=review_data,
            headers=reviewer_headers
        )
        # 根据实际实现，可能返回200或404（如果没有审核功能）
        if response.status_code == 200:
            reviewed_task = response.json()
            assert reviewed_task["status"] == "approved"
    
    def test_annotation_task_assignment_workflow(self, client: TestClient, db: Session):
        """测试标注任务分配工作流"""
        UserFactory._meta.sqlalchemy_session = db
        AdminUserFactory._meta.sqlalchemy_session = db
        
        admin = AdminUserFactory()
        annotator1 = UserFactory(role="annotator")
        annotator2 = UserFactory(role="annotator")
        
        admin_headers = create_auth_headers(admin)
        
        # 1. 创建未分配的任务
        task_data = {
            "title": "未分配的标注任务",
            "description": "等待分配的任务",
            "task_type": "difficulty_rating",
            "priority": "low"
        }
        
        response = client.post(
            "/api/v1/annotation/tasks/",
            json=task_data,
            headers=admin_headers
        )
        assert_response_success(response, 201)
        task = response.json()
        
        assert task["assigned_to"] is None
        assert task["status"] == "pending"
        
        # 2. 管理员分配任务给标注员1
        response = client.post(
            f"/api/v1/annotation/tasks/{task['task_id']}/assign",
            json={"assignee_id": annotator1.id},
            headers=admin_headers
        )
        assert_response_success(response, 200)
        assigned_task = response.json()
        
        assert assigned_task["assigned_to"] == annotator1.id
        
        # 3. 重新分配给标注员2
        response = client.post(
            f"/api/v1/annotation/tasks/{task['task_id']}/assign",
            json={"assignee_id": annotator2.id},
            headers=admin_headers
        )
        assert_response_success(response, 200)
        reassigned_task = response.json()
        
        assert reassigned_task["assigned_to"] == annotator2.id
        
        # 4. 批量分配任务
        # 创建多个未分配的任务
        task_ids = []
        for i in range(3):
            task_data = {
                "title": f"批量任务{i+1}",
                "task_type": "quality_review",
                "priority": "medium"
            }
            
            response = client.post(
                "/api/v1/annotation/tasks/",
                json=task_data,
                headers=admin_headers
            )
            task_ids.append(response.json()["task_id"])
        
        # 批量分配给标注员1
        response = client.post(
            "/api/v1/annotation/tasks/bulk-assign",
            json={
                "task_ids": task_ids,
                "assignee_id": annotator1.id
            },
            headers=admin_headers
        )
        
        if response.status_code == 200:
            result = response.json()
            assert result["assigned_count"] == 3
    
    def test_annotation_quality_control_workflow(self, client: TestClient, db: Session):
        """测试标注质量控制工作流"""
        UserFactory._meta.sqlalchemy_session = db
        AdminUserFactory._meta.sqlalchemy_session = db
        
        admin = AdminUserFactory()
        annotator = UserFactory(role="annotator")
        reviewer = UserFactory(role="reviewer")
        
        admin_headers = create_auth_headers(admin)
        annotator_headers = create_auth_headers(annotator)
        reviewer_headers = create_auth_headers(reviewer)
        
        # 1. 创建需要质量控制的任务
        task_data = {
            "title": "质量控制任务",
            "description": "需要严格质量控制的标注任务",
            "task_type": "knowledge_point_mapping",
            "priority": "high",
            "assigned_to": annotator.id,
            "metadata": {
                "quality_control": True,
                "min_quality_score": 0.8,
                "require_review": True
            }
        }
        
        response = client.post(
            "/api/v1/annotation/tasks/",
            json=task_data,
            headers=admin_headers
        )
        assert_response_success(response, 201)
        task = response.json()
        
        # 2. 标注员完成任务（质量较低）
        response = client.post(
            f"/api/v1/annotation/tasks/{task['task_id']}/start",
            headers=annotator_headers
        )
        
        low_quality_result = {
            "knowledge_points": [],  # 空结果
            "confidence": 0.3,  # 低置信度
            "notes": "不确定"
        }
        
        response = client.post(
            f"/api/v1/annotation/tasks/{task['task_id']}/submit",
            json={"result": low_quality_result},
            headers=annotator_headers
        )
        assert_response_success(response, 200)
        
        # 3. 审核员拒绝低质量标注
        review_data = {
            "status": "rejected",
            "quality_score": 0.3,
            "feedback": "标注质量不符合要求，请重新标注"
        }
        
        response = client.post(
            f"/api/v1/annotation/tasks/{task['task_id']}/review",
            json=review_data,
            headers=reviewer_headers
        )
        
        if response.status_code == 200:
            # 4. 任务重新分配给标注员
            response = client.post(
                f"/api/v1/annotation/tasks/{task['task_id']}/reassign",
                json={"assignee_id": annotator.id},
                headers=admin_headers
            )
            
            # 5. 标注员重新标注（高质量）
            response = client.post(
                f"/api/v1/annotation/tasks/{task['task_id']}/start",
                headers=annotator_headers
            )
            
            high_quality_result = {
                "knowledge_points": [1, 2],  # 有效结果
                "confidence": 0.9,  # 高置信度
                "notes": "经过仔细分析的结果"
            }
            
            response = client.post(
                f"/api/v1/annotation/tasks/{task['task_id']}/submit",
                json={"result": high_quality_result},
                headers=annotator_headers
            )
            
            # 6. 审核员批准高质量标注
            review_data = {
                "status": "approved",
                "quality_score": 0.9,
                "feedback": "标注质量良好"
            }
            
            response = client.post(
                f"/api/v1/annotation/tasks/{task['task_id']}/review",
                json=review_data,
                headers=reviewer_headers
            )
    
    def test_annotation_statistics_workflow(self, client: TestClient, db: Session):
        """测试标注统计工作流"""
        UserFactory._meta.sqlalchemy_session = db
        AdminUserFactory._meta.sqlalchemy_session = db
        
        admin = AdminUserFactory()
        annotator = UserFactory(role="annotator")
        
        admin_headers = create_auth_headers(admin)
        annotator_headers = create_auth_headers(annotator)
        
        # 1. 创建多个不同状态的任务
        task_statuses = ["pending", "in_progress", "completed", "cancelled"]
        created_tasks = []
        
        for i, status in enumerate(task_statuses):
            task_data = {
                "title": f"统计测试任务{i+1}",
                "task_type": "knowledge_point_mapping",
                "priority": "medium",
                "assigned_to": annotator.id
            }
            
            response = client.post(
                "/api/v1/annotation/tasks/",
                json=task_data,
                headers=admin_headers
            )
            task = response.json()
            created_tasks.append(task)
            
            # 根据需要的状态更新任务
            if status == "in_progress":
                client.post(
                    f"/api/v1/annotation/tasks/{task['task_id']}/start",
                    headers=annotator_headers
                )
            elif status == "completed":
                client.post(
                    f"/api/v1/annotation/tasks/{task['task_id']}/start",
                    headers=annotator_headers
                )
                client.post(
                    f"/api/v1/annotation/tasks/{task['task_id']}/submit",
                    json={"result": {"knowledge_points": [1]}},
                    headers=annotator_headers
                )
            elif status == "cancelled":
                client.post(
                    f"/api/v1/annotation/tasks/{task['task_id']}/cancel",
                    json={"reason": "测试取消"},
                    headers=admin_headers
                )
        
        # 2. 获取系统统计信息
        response = client.get(
            "/api/v1/annotation/statistics",
            headers=admin_headers
        )
        
        if response.status_code == 200:
            stats = response.json()
            
            assert "total_tasks" in stats
            assert "by_status" in stats
            assert stats["total_tasks"] >= 4
        
        # 3. 获取用户统计信息
        response = client.get(
            f"/api/v1/annotation/users/{annotator.id}/statistics",
            headers=admin_headers
        )
        
        if response.status_code == 200:
            user_stats = response.json()
            
            assert "total_assigned" in user_stats
            assert "completed" in user_stats
            assert user_stats["total_assigned"] >= 4
        
        # 4. 获取任务类型统计
        response = client.get(
            "/api/v1/annotation/statistics/by-type",
            headers=admin_headers
        )
        
        if response.status_code == 200:
            type_stats = response.json()
            assert "knowledge_point_mapping" in type_stats
