# 后端单元测试文档

本文档介绍了自适应学习数据标注系统后端的单元测试框架和使用方法。

## 测试结构

```
tests/
├── __init__.py                 # 测试包初始化
├── conftest.py                 # pytest配置和全局fixtures
├── README.md                   # 本文档
├── unit/                       # 单元测试
│   ├── __init__.py
│   ├── models/                 # 模型层测试
│   │   ├── __init__.py
│   │   ├── test_user.py        # 用户模型测试
│   │   ├── test_knowledge.py   # 知识点模型测试
│   │   ├── test_question.py    # 题目模型测试
│   │   └── test_annotation.py  # 标注模型测试
│   └── services/               # 服务层测试
│       ├── __init__.py
│       ├── test_knowledge_service.py    # 知识点服务测试
│       ├── test_user_service.py         # 用户服务测试
│       └── test_annotation_service.py   # 标注服务测试
├── api/                        # API端点测试
│   ├── __init__.py
│   ├── test_auth.py            # 认证API测试
│   ├── test_knowledge_points.py # 知识点API测试
│   └── test_users.py           # 用户管理API测试
├── integration/                # 集成测试
│   ├── __init__.py
│   ├── test_knowledge_workflow.py    # 知识点工作流测试
│   └── test_annotation_workflow.py   # 标注工作流测试
└── utils/                      # 测试工具
    ├── __init__.py
    ├── factories.py            # 数据工厂
    └── helpers.py              # 辅助函数
```

## 测试类型

### 1. 单元测试 (Unit Tests)
- **位置**: `tests/unit/`
- **标记**: `@pytest.mark.unit`
- **目的**: 测试单个函数、方法或类的功能
- **特点**: 快速执行，隔离性强

### 2. 集成测试 (Integration Tests)
- **位置**: `tests/integration/`
- **标记**: `@pytest.mark.integration`
- **目的**: 测试多个组件之间的交互
- **特点**: 测试完整的业务流程

### 3. API测试 (API Tests)
- **位置**: `tests/api/`
- **标记**: `@pytest.mark.api`
- **目的**: 测试HTTP API端点
- **特点**: 使用TestClient模拟HTTP请求

## 运行测试

### 使用Python脚本

```bash
# 运行所有测试
python scripts/run_tests.py

# 运行特定类型的测试
python scripts/run_tests.py --type unit
python scripts/run_tests.py --type integration
python scripts/run_tests.py --type api

# 生成覆盖率报告
python scripts/run_tests.py --coverage

# 检查代码质量
python scripts/run_tests.py --quality

# 运行特定标记的测试
python scripts/run_tests.py --markers "unit and db"

# 运行特定测试文件
python scripts/run_tests.py --test tests/unit/models/test_user.py
```

### 使用Shell脚本

```bash
# 运行所有测试
./scripts/test_commands.sh all

# 运行特定类型的测试
./scripts/test_commands.sh unit
./scripts/test_commands.sh integration
./scripts/test_commands.sh api

# 生成覆盖率报告
./scripts/test_commands.sh coverage

# 运行特定模块测试
./scripts/test_commands.sh models
./scripts/test_commands.sh services
./scripts/test_commands.sh auth
./scripts/test_commands.sh knowledge

# 检查代码质量
./scripts/test_commands.sh quality

# 修复代码格式
./scripts/test_commands.sh format

# 清理测试文件
./scripts/test_commands.sh cleanup
```

### 使用Makefile

```bash
# 运行所有测试
make test

# 运行特定类型的测试
make test-unit
make test-integration
make test-api

# 生成覆盖率报告
make test-coverage

# 检查代码质量
make quality

# 格式化代码
make format

# 清理测试文件
make clean
```

### 直接使用pytest

```bash
# 运行所有测试
pytest tests/

# 运行单元测试
pytest tests/unit/ -m unit

# 运行集成测试
pytest tests/integration/ -m integration

# 运行API测试
pytest tests/api/ -m api

# 生成覆盖率报告
pytest tests/ --cov=app --cov-report=html

# 运行特定测试文件
pytest tests/unit/models/test_user.py -v

# 运行特定测试函数
pytest tests/unit/models/test_user.py::TestUserModel::test_create_user -v
```

## 测试配置

### 环境变量
测试运行时会自动设置以下环境变量：
- `TESTING=true`: 标识测试环境
- `DATABASE_URL=sqlite:///./test.db`: 使用SQLite测试数据库

### Fixtures
在`conftest.py`中定义了以下全局fixtures：

- `db`: 数据库会话，每个测试函数都会获得干净的数据库
- `client`: FastAPI测试客户端
- `async_client`: 异步测试客户端
- `test_user_data`: 测试用户数据
- `test_knowledge_point_data`: 测试知识点数据
- `test_question_data`: 测试题目数据
- `test_annotation_task_data`: 测试标注任务数据

### 数据工厂
使用Factory Boy创建测试数据：

```python
from tests.utils.factories import UserFactory, KnowledgePointFactory

# 创建测试用户
user = UserFactory()

# 创建测试知识点
kp = KnowledgePointFactory(subject="数学")

# 创建知识点层次结构
hierarchy = create_test_knowledge_hierarchy(db, "数学")
```

## 测试标记

使用pytest标记来分类测试：

```python
@pytest.mark.unit          # 单元测试
@pytest.mark.integration   # 集成测试
@pytest.mark.api          # API测试
@pytest.mark.db           # 需要数据库的测试
@pytest.mark.slow         # 慢速测试
```

运行特定标记的测试：
```bash
pytest -m "unit and db"     # 运行需要数据库的单元测试
pytest -m "not slow"        # 跳过慢速测试
```

## 覆盖率报告

生成覆盖率报告：
```bash
pytest tests/ --cov=app --cov-report=html --cov-report=term-missing
```

报告文件：
- HTML报告: `htmlcov/index.html`
- XML报告: `coverage.xml`
- 终端报告: 显示缺失覆盖的行

## 代码质量检查

### Black (代码格式化)
```bash
black --check app/ tests/    # 检查格式
black app/ tests/            # 格式化代码
```

### isort (导入排序)
```bash
isort --check-only app/ tests/    # 检查导入排序
isort app/ tests/                 # 排序导入
```

### flake8 (代码风格)
```bash
flake8 app/ tests/
```

### mypy (类型检查)
```bash
mypy app/
```

## 持续集成

GitHub Actions配置文件位于`.github/workflows/tests.yml`，包含：

1. **多Python版本测试**: Python 3.11, 3.12
2. **代码质量检查**: black, isort, flake8, mypy
3. **测试执行**: 单元测试、集成测试、API测试
4. **覆盖率报告**: 上传到Codecov
5. **安全扫描**: bandit, safety
6. **性能测试**: 使用Locust
7. **Docker构建测试**: 验证容器化部署

## 最佳实践

### 1. 测试命名
- 测试文件: `test_*.py`
- 测试类: `Test*`
- 测试函数: `test_*`

### 2. 测试结构
```python
def test_function_name():
    # Arrange (准备)
    user = UserFactory()
    
    # Act (执行)
    result = service.create_user(user_data)
    
    # Assert (断言)
    assert result.username == user_data["username"]
```

### 3. 使用辅助函数
```python
from tests.utils.helpers import (
    assert_response_success,
    assert_response_error,
    create_auth_headers
)

def test_api_endpoint():
    headers = create_auth_headers(user)
    response = client.get("/api/endpoint", headers=headers)
    assert_response_success(response, 200)
```

### 4. 数据隔离
每个测试都应该使用独立的数据，避免测试之间的相互影响。

### 5. 模拟外部依赖
使用mock来模拟外部服务和依赖：
```python
from unittest.mock import patch

@patch('app.services.external_service.call_api')
def test_with_mock(mock_call):
    mock_call.return_value = {"status": "success"}
    # 测试代码
```

## 故障排除

### 常见问题

1. **数据库连接错误**
   - 确保测试环境变量正确设置
   - 检查数据库文件权限

2. **导入错误**
   - 确保PYTHONPATH包含项目根目录
   - 检查模块路径是否正确

3. **测试数据冲突**
   - 使用工厂创建独立的测试数据
   - 确保每个测试都有干净的数据库状态

4. **权限错误**
   - 确保测试脚本有执行权限
   - 检查文件和目录权限

### 调试技巧

1. **使用pytest的调试选项**
   ```bash
   pytest -v -s --tb=short    # 详细输出和短回溯
   pytest --pdb              # 进入调试器
   ```

2. **查看测试覆盖率**
   ```bash
   pytest --cov=app --cov-report=term-missing
   ```

3. **运行特定测试**
   ```bash
   pytest tests/unit/models/test_user.py::TestUserModel::test_create_user -v
   ```

## 贡献指南

1. 为新功能编写测试
2. 确保测试覆盖率不低于80%
3. 运行代码质量检查
4. 更新相关文档
5. 提交前运行完整测试套件
