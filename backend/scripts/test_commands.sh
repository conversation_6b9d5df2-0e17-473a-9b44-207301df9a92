#!/bin/bash
"""
测试命令脚本
提供常用的测试命令快捷方式
"""

# 设置脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 进入项目目录
cd "$PROJECT_DIR"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    print_info "检查测试依赖..."
    
    # 检查Python
    if ! command -v python &> /dev/null; then
        print_error "Python未安装"
        exit 1
    fi
    
    # 检查pytest
    if ! python -c "import pytest" &> /dev/null; then
        print_error "pytest未安装，请运行: pip install pytest"
        exit 1
    fi
    
    print_success "依赖检查通过"
}

# 设置测试环境
setup_test_env() {
    print_info "设置测试环境..."
    
    export TESTING=true
    export DATABASE_URL="sqlite:///./test.db"
    
    # 清理旧的测试数据库
    if [ -f "test.db" ]; then
        rm test.db
        print_info "清理旧的测试数据库"
    fi
    
    print_success "测试环境设置完成"
}

# 运行单元测试
run_unit_tests() {
    print_info "运行单元测试..."
    python -m pytest tests/unit/ -v -m unit
}

# 运行集成测试
run_integration_tests() {
    print_info "运行集成测试..."
    python -m pytest tests/integration/ -v -m integration
}

# 运行API测试
run_api_tests() {
    print_info "运行API测试..."
    python -m pytest tests/api/ -v -m api
}

# 运行所有测试
run_all_tests() {
    print_info "运行所有测试..."
    python -m pytest tests/ -v
}

# 运行测试并生成覆盖率报告
run_tests_with_coverage() {
    print_info "运行测试并生成覆盖率报告..."
    python -m pytest tests/ \
        --cov=app \
        --cov-report=term-missing \
        --cov-report=html \
        --cov-report=xml \
        -v
    
    if [ $? -eq 0 ]; then
        print_success "覆盖率报告生成完成: htmlcov/index.html"
    fi
}

# 运行快速测试（跳过慢速测试）
run_fast_tests() {
    print_info "运行快速测试..."
    python -m pytest tests/ -v -m "not slow"
}

# 运行特定模块的测试
run_model_tests() {
    print_info "运行模型测试..."
    python -m pytest tests/unit/models/ -v
}

run_service_tests() {
    print_info "运行服务测试..."
    python -m pytest tests/unit/services/ -v
}

run_auth_tests() {
    print_info "运行认证测试..."
    python -m pytest tests/api/test_auth.py -v
}

run_knowledge_tests() {
    print_info "运行知识点测试..."
    python -m pytest tests/api/test_knowledge_points.py tests/unit/models/test_knowledge.py tests/unit/services/test_knowledge_service.py -v
}

# 代码质量检查
check_code_quality() {
    print_info "检查代码质量..."
    
    # Black格式检查
    print_info "检查代码格式..."
    if ! black --check app/ tests/; then
        print_warning "代码格式需要修复，运行: black app/ tests/"
    fi
    
    # isort导入排序检查
    print_info "检查导入排序..."
    if ! isort --check-only app/ tests/; then
        print_warning "导入排序需要修复，运行: isort app/ tests/"
    fi
    
    # flake8代码风格检查
    print_info "检查代码风格..."
    if ! flake8 app/ tests/; then
        print_warning "代码风格需要修复"
    fi
    
    # mypy类型检查
    print_info "检查类型注解..."
    if ! mypy app/; then
        print_warning "类型注解需要修复"
    fi
}

# 修复代码格式
fix_code_format() {
    print_info "修复代码格式..."
    
    # 运行black格式化
    black app/ tests/
    
    # 运行isort排序
    isort app/ tests/
    
    print_success "代码格式修复完成"
}

# 清理测试文件
cleanup_test_files() {
    print_info "清理测试文件..."
    
    # 删除测试数据库
    if [ -f "test.db" ]; then
        rm test.db
    fi
    
    # 删除覆盖率文件
    if [ -f ".coverage" ]; then
        rm .coverage
    fi
    
    # 删除覆盖率报告目录
    if [ -d "htmlcov" ]; then
        rm -rf htmlcov
    fi
    
    # 删除pytest缓存
    if [ -d ".pytest_cache" ]; then
        rm -rf .pytest_cache
    fi
    
    # 删除__pycache__目录
    find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
    
    print_success "测试文件清理完成"
}

# 显示帮助信息
show_help() {
    echo "测试命令脚本使用说明:"
    echo ""
    echo "基本命令:"
    echo "  ./scripts/test_commands.sh unit              - 运行单元测试"
    echo "  ./scripts/test_commands.sh integration       - 运行集成测试"
    echo "  ./scripts/test_commands.sh api               - 运行API测试"
    echo "  ./scripts/test_commands.sh all               - 运行所有测试"
    echo "  ./scripts/test_commands.sh coverage          - 运行测试并生成覆盖率报告"
    echo "  ./scripts/test_commands.sh fast              - 运行快速测试"
    echo ""
    echo "模块测试:"
    echo "  ./scripts/test_commands.sh models            - 运行模型测试"
    echo "  ./scripts/test_commands.sh services          - 运行服务测试"
    echo "  ./scripts/test_commands.sh auth              - 运行认证测试"
    echo "  ./scripts/test_commands.sh knowledge         - 运行知识点相关测试"
    echo ""
    echo "代码质量:"
    echo "  ./scripts/test_commands.sh quality           - 检查代码质量"
    echo "  ./scripts/test_commands.sh format            - 修复代码格式"
    echo ""
    echo "工具命令:"
    echo "  ./scripts/test_commands.sh cleanup           - 清理测试文件"
    echo "  ./scripts/test_commands.sh help              - 显示帮助信息"
    echo ""
}

# 主函数
main() {
    # 检查依赖
    check_dependencies
    
    # 设置测试环境
    setup_test_env
    
    # 根据参数执行相应命令
    case "${1:-help}" in
        "unit")
            run_unit_tests
            ;;
        "integration")
            run_integration_tests
            ;;
        "api")
            run_api_tests
            ;;
        "all")
            run_all_tests
            ;;
        "coverage")
            run_tests_with_coverage
            ;;
        "fast")
            run_fast_tests
            ;;
        "models")
            run_model_tests
            ;;
        "services")
            run_service_tests
            ;;
        "auth")
            run_auth_tests
            ;;
        "knowledge")
            run_knowledge_tests
            ;;
        "quality")
            check_code_quality
            ;;
        "format")
            fix_code_format
            ;;
        "cleanup")
            cleanup_test_files
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 运行主函数
main "$@"
