#!/usr/bin/env python3
"""
测试运行脚本
提供不同类型的测试运行选项
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path


def run_command(cmd, cwd=None):
    """运行命令并返回结果"""
    print(f"运行命令: {' '.join(cmd)}")
    result = subprocess.run(cmd, cwd=cwd, capture_output=True, text=True)
    
    if result.stdout:
        print(result.stdout)
    if result.stderr:
        print(result.stderr, file=sys.stderr)
    
    return result.returncode == 0


def setup_test_environment():
    """设置测试环境"""
    print("设置测试环境...")
    
    # 设置环境变量
    os.environ["TESTING"] = "true"
    os.environ["DATABASE_URL"] = "sqlite:///./test.db"
    
    # 确保测试数据库目录存在
    test_db_path = Path("./test.db")
    if test_db_path.exists():
        test_db_path.unlink()
    
    print("测试环境设置完成")


def run_unit_tests(coverage=False, verbose=False):
    """运行单元测试"""
    print("运行单元测试...")
    
    cmd = ["python", "-m", "pytest", "tests/unit/"]
    
    if coverage:
        cmd.extend(["--cov=app", "--cov-report=term-missing", "--cov-report=html"])
    
    if verbose:
        cmd.append("-v")
    
    cmd.extend(["-m", "unit"])
    
    return run_command(cmd)


def run_integration_tests(verbose=False):
    """运行集成测试"""
    print("运行集成测试...")
    
    cmd = ["python", "-m", "pytest", "tests/integration/"]
    
    if verbose:
        cmd.append("-v")
    
    cmd.extend(["-m", "integration"])
    
    return run_command(cmd)


def run_api_tests(verbose=False):
    """运行API测试"""
    print("运行API测试...")
    
    cmd = ["python", "-m", "pytest", "tests/api/"]
    
    if verbose:
        cmd.append("-v")
    
    cmd.extend(["-m", "api"])
    
    return run_command(cmd)


def run_all_tests(coverage=False, verbose=False):
    """运行所有测试"""
    print("运行所有测试...")
    
    cmd = ["python", "-m", "pytest", "tests/"]
    
    if coverage:
        cmd.extend(["--cov=app", "--cov-report=term-missing", "--cov-report=html"])
    
    if verbose:
        cmd.append("-v")
    
    return run_command(cmd)


def run_specific_test(test_path, verbose=False):
    """运行特定测试"""
    print(f"运行特定测试: {test_path}")
    
    cmd = ["python", "-m", "pytest", test_path]
    
    if verbose:
        cmd.append("-v")
    
    return run_command(cmd)


def run_tests_with_markers(markers, coverage=False, verbose=False):
    """运行带特定标记的测试"""
    print(f"运行标记为 {markers} 的测试...")
    
    cmd = ["python", "-m", "pytest"]
    
    if coverage:
        cmd.extend(["--cov=app", "--cov-report=term-missing"])
    
    if verbose:
        cmd.append("-v")
    
    cmd.extend(["-m", markers])
    
    return run_command(cmd)


def check_code_quality():
    """检查代码质量"""
    print("检查代码质量...")
    
    success = True
    
    # 运行 black 格式检查
    print("检查代码格式 (black)...")
    if not run_command(["black", "--check", "app/", "tests/"]):
        print("代码格式检查失败")
        success = False
    
    # 运行 isort 导入排序检查
    print("检查导入排序 (isort)...")
    if not run_command(["isort", "--check-only", "app/", "tests/"]):
        print("导入排序检查失败")
        success = False
    
    # 运行 flake8 代码风格检查
    print("检查代码风格 (flake8)...")
    if not run_command(["flake8", "app/", "tests/"]):
        print("代码风格检查失败")
        success = False
    
    # 运行 mypy 类型检查
    print("检查类型注解 (mypy)...")
    if not run_command(["mypy", "app/"]):
        print("类型检查失败")
        success = False
    
    return success


def generate_test_report():
    """生成测试报告"""
    print("生成测试报告...")
    
    # 运行测试并生成覆盖率报告
    cmd = [
        "python", "-m", "pytest",
        "--cov=app",
        "--cov-report=html",
        "--cov-report=xml",
        "--cov-report=term-missing",
        "--junitxml=test-results.xml"
    ]
    
    success = run_command(cmd)
    
    if success:
        print("测试报告生成完成:")
        print("- HTML覆盖率报告: htmlcov/index.html")
        print("- XML覆盖率报告: coverage.xml")
        print("- JUnit测试结果: test-results.xml")
    
    return success


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="运行测试脚本")
    parser.add_argument("--type", choices=["unit", "integration", "api", "all"], 
                       default="all", help="测试类型")
    parser.add_argument("--coverage", action="store_true", help="生成覆盖率报告")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    parser.add_argument("--quality", action="store_true", help="检查代码质量")
    parser.add_argument("--report", action="store_true", help="生成测试报告")
    parser.add_argument("--markers", help="运行特定标记的测试")
    parser.add_argument("--test", help="运行特定测试文件或函数")
    
    args = parser.parse_args()
    
    # 设置测试环境
    setup_test_environment()
    
    success = True
    
    # 检查代码质量
    if args.quality:
        if not check_code_quality():
            success = False
            print("代码质量检查失败")
    
    # 运行测试
    if args.test:
        success = run_specific_test(args.test, args.verbose)
    elif args.markers:
        success = run_tests_with_markers(args.markers, args.coverage, args.verbose)
    elif args.report:
        success = generate_test_report()
    else:
        if args.type == "unit":
            success = run_unit_tests(args.coverage, args.verbose)
        elif args.type == "integration":
            success = run_integration_tests(args.verbose)
        elif args.type == "api":
            success = run_api_tests(args.verbose)
        elif args.type == "all":
            success = run_all_tests(args.coverage, args.verbose)
    
    if success:
        print("✅ 所有测试通过!")
        sys.exit(0)
    else:
        print("❌ 测试失败!")
        sys.exit(1)


if __name__ == "__main__":
    main()
