name: 后端测试

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'backend/**'
      - '.github/workflows/tests.yml'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'backend/**'
      - '.github/workflows/tests.yml'

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.11, 3.12]
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - uses: actions/checkout@v4

    - name: 设置Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: 缓存pip依赖
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: 安装依赖
      working-directory: ./backend
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -e ".[test]"

    - name: 代码质量检查
      working-directory: ./backend
      run: |
        # Black格式检查
        black --check app/ tests/
        
        # isort导入排序检查
        isort --check-only app/ tests/
        
        # flake8代码风格检查
        flake8 app/ tests/
        
        # mypy类型检查
        mypy app/

    - name: 运行单元测试
      working-directory: ./backend
      env:
        TESTING: true
        DATABASE_URL: sqlite:///./test.db
      run: |
        python -m pytest tests/unit/ -v -m unit --cov=app --cov-report=xml

    - name: 运行集成测试
      working-directory: ./backend
      env:
        TESTING: true
        DATABASE_URL: sqlite:///./test.db
      run: |
        python -m pytest tests/integration/ -v -m integration

    - name: 运行API测试
      working-directory: ./backend
      env:
        TESTING: true
        DATABASE_URL: sqlite:///./test.db
      run: |
        python -m pytest tests/api/ -v -m api

    - name: 运行PostgreSQL测试
      working-directory: ./backend
      env:
        TESTING: true
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
      run: |
        python -m pytest tests/unit/models/ -v -m "unit and db"

    - name: 上传覆盖率报告
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/coverage.xml
        flags: backend
        name: backend-coverage
        fail_ci_if_error: false

  security-scan:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: 设置Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.11

    - name: 安装安全扫描工具
      run: |
        pip install bandit safety

    - name: 运行Bandit安全扫描
      working-directory: ./backend
      run: |
        bandit -r app/ -f json -o bandit-report.json || true
        bandit -r app/

    - name: 运行Safety依赖安全检查
      working-directory: ./backend
      run: |
        safety check --json --output safety-report.json || true
        safety check

    - name: 上传安全报告
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-reports
        path: |
          backend/bandit-report.json
          backend/safety-report.json

  performance-test:
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    
    steps:
    - uses: actions/checkout@v4

    - name: 设置Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.11

    - name: 安装依赖
      working-directory: ./backend
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -e ".[test]"
        pip install locust

    - name: 运行性能测试
      working-directory: ./backend
      env:
        TESTING: true
        DATABASE_URL: sqlite:///./test.db
      run: |
        # 启动应用
        uvicorn app.main:app --host 0.0.0.0 --port 8000 &
        APP_PID=$!
        
        # 等待应用启动
        sleep 10
        
        # 运行性能测试
        locust -f tests/performance/locustfile.py --headless -u 10 -r 2 -t 30s --host http://localhost:8000 || true
        
        # 停止应用
        kill $APP_PID

  build-test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: 构建Docker镜像
      working-directory: ./backend
      run: |
        docker build -t backend-test .

    - name: 运行Docker容器测试
      run: |
        docker run --rm -e TESTING=true backend-test python -m pytest tests/unit/ -v -m unit
