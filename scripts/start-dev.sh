#!/bin/bash

# 自适应学习数据标注系统 - 开发环境启动脚本
# 使用Supabase作为PostgreSQL数据库，Dapr管理Redis服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_step() {
    echo -e "${PURPLE}🔧 $1${NC}"
}

echo -e "${CYAN}🚀 启动自适应学习数据标注系统开发环境${NC}"
echo ""

# 检查必要的工具
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 未安装"
        case $1 in
            "python3")
                echo "   请安装Python 3.8+: https://www.python.org/downloads/"
                ;;
            "node")
                echo "   请安装Node.js 18+: https://nodejs.org/"
                ;;
            "npm")
                echo "   npm通常随Node.js一起安装"
                ;;
            "dapr")
                echo "   请安装Dapr CLI: https://docs.dapr.io/getting-started/install-dapr-cli/"
                ;;
            "docker")
                echo "   请安装Docker: https://docs.docker.com/get-docker/"
                ;;
        esac
        return 1
    fi
    return 0
}

# 检查工具版本
check_version() {
    local tool=$1
    local min_version=$2
    local current_version=$3

    if [[ $(echo "$current_version < $min_version" | bc -l 2>/dev/null || echo "1") -eq 1 ]]; then
        log_error "$tool 版本过低: $current_version (需要 >= $min_version)"
        return 1
    fi
    return 0
}

log_step "检查系统依赖..."

# 检查基础工具
MISSING_TOOLS=()
for tool in python3 node npm docker; do
    if ! check_command $tool; then
        MISSING_TOOLS+=($tool)
    fi
done

if [ ${#MISSING_TOOLS[@]} -ne 0 ]; then
    log_error "缺少必要工具: ${MISSING_TOOLS[*]}"
    exit 1
fi

# 检查Python版本
PYTHON_VERSION=$(python3 --version 2>/dev/null | cut -d' ' -f2 | cut -d'.' -f1,2)
if ! check_version "Python" "3.8" "$PYTHON_VERSION"; then
    exit 1
fi

# 检查Node版本
NODE_VERSION=$(node --version 2>/dev/null | cut -d'v' -f2 | cut -d'.' -f1)
if [[ $NODE_VERSION -lt 18 ]]; then
    log_error "Node.js版本过低: $NODE_VERSION (需要 >= 18)"
    exit 1
fi

# 检查Dapr CLI（可选）
DAPR_AVAILABLE=false
if check_command "dapr"; then
    DAPR_AVAILABLE=true
    log_success "Dapr CLI 可用"
else
    log_warning "Dapr CLI 未安装，将跳过Dapr相关功能"
fi

log_success "系统依赖检查通过"
echo ""

# 环境变量检查
log_step "检查环境变量配置..."

# 默认的Supabase连接字符串
DEFAULT_DATABASE_URL="postgresql://postgres.qlpqyctsezzfzdgvfvir:<EMAIL>:6543/postgres"

# 检查数据库连接字符串
if [ -z "$DATABASE_URL" ]; then
    log_warning "DATABASE_URL 环境变量未设置，使用默认Supabase连接"
    export DATABASE_URL="$DEFAULT_DATABASE_URL"
else
    log_info "使用自定义数据库连接: ${DATABASE_URL:0:30}..."
fi

# 验证数据库连接
log_step "验证Supabase数据库连接..."
if command -v psql &> /dev/null; then
    if psql "$DATABASE_URL" -c "SELECT 1;" &> /dev/null; then
        log_success "Supabase数据库连接正常"
    else
        log_error "无法连接到Supabase数据库"
        log_info "请检查网络连接和数据库配置"
        exit 1
    fi
else
    log_warning "psql未安装，跳过数据库连接测试"
    log_info "建议安装PostgreSQL客户端工具进行连接测试"
fi

# 检查Redis服务（通过Dapr）
log_step "检查Redis服务状态..."
REDIS_AVAILABLE=false

# 检查本地Redis是否运行
if command -v redis-cli &> /dev/null; then
    if redis-cli -p 6379 ping &> /dev/null; then
        REDIS_AVAILABLE=true
        log_success "Redis服务运行正常 (端口 6379)"
    elif redis-cli -p 6380 ping &> /dev/null; then
        REDIS_AVAILABLE=true
        log_success "Redis服务运行正常 (端口 6380)"
    fi
fi

if [ "$REDIS_AVAILABLE" = false ]; then
    log_warning "Redis服务未运行，尝试启动Docker Redis容器..."
    if docker run -d --name annotation-redis -p 6379:6379 redis:7-alpine redis-server --appendonly yes &> /dev/null; then
        log_success "Redis容器启动成功"
        REDIS_AVAILABLE=true
        # 等待Redis启动
        sleep 3
    else
        # 可能容器已存在，尝试启动
        if docker start annotation-redis &> /dev/null; then
            log_success "Redis容器重新启动成功"
            REDIS_AVAILABLE=true
            sleep 3
        else
            log_warning "无法启动Redis服务，某些功能可能受限"
        fi
    fi
fi

# 设置其他环境变量
export SECRET_KEY="${SECRET_KEY:-dev-secret-key-change-in-production}"
export DEBUG="${DEBUG:-true}"
export ENVIRONMENT="${ENVIRONMENT:-development}"
export CORS_ORIGINS="${CORS_ORIGINS:-http://localhost:3000,http://127.0.0.1:3000}"

log_success "环境配置检查完成"
echo ""

# 后端服务启动
log_step "设置后端Python环境..."

# 进入后端目录
if [ ! -d "backend" ]; then
    log_error "backend目录不存在"
    exit 1
fi

cd backend

# 创建虚拟环境（如果不存在）
if [ ! -d "venv" ]; then
    log_info "创建Python虚拟环境..."
    python3 -m venv venv
    if [ $? -ne 0 ]; then
        log_error "创建虚拟环境失败"
        exit 1
    fi
fi

# 激活虚拟环境
log_info "激活Python虚拟环境..."
source venv/bin/activate

# 检查requirements.txt
if [ ! -f "requirements.txt" ]; then
    log_error "requirements.txt文件不存在"
    exit 1
fi

# 安装依赖
log_info "安装Python依赖包..."
pip install --upgrade pip > /dev/null 2>&1
pip install -r requirements.txt

if [ $? -ne 0 ]; then
    log_error "安装Python依赖失败"
    exit 1
fi

# 检查数据库迁移
log_step "检查数据库迁移..."
if [ -f "alembic.ini" ] && [ -d "alembic" ]; then
    log_info "运行数据库迁移..."
    alembic upgrade head
    if [ $? -ne 0 ]; then
        log_warning "数据库迁移失败，可能需要手动处理"
    else
        log_success "数据库迁移完成"
    fi
else
    log_warning "未找到Alembic配置，跳过数据库迁移"
fi

# 启动后端服务
log_step "启动FastAPI后端服务..."
log_info "后端服务将在 http://localhost:8000 启动"
log_info "API文档将在 http://localhost:8000/docs 可用"

# 检查端口是否被占用
if lsof -i :8000 &> /dev/null; then
    log_warning "端口8000已被占用，尝试终止现有进程..."
    lsof -ti :8000 | xargs kill -9 2>/dev/null || true
    sleep 2
fi

# 启动后端服务（后台运行）
nohup uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload > ../logs/backend.log 2>&1 &
BACKEND_PID=$!

# 等待后端启动
log_info "等待后端服务启动..."
for i in {1..30}; do
    if curl -s http://localhost:8000/health > /dev/null 2>&1; then
        log_success "后端服务启动成功 (PID: $BACKEND_PID)"
        break
    fi
    if [ $i -eq 30 ]; then
        log_error "后端服务启动超时"
        kill $BACKEND_PID 2>/dev/null || true
        exit 1
    fi
    sleep 1
done

cd ..
echo ""

# 前端服务启动
log_step "设置前端React环境..."

# 进入前端目录
if [ ! -d "frontend" ]; then
    log_error "frontend目录不存在"
    kill $BACKEND_PID 2>/dev/null || true
    exit 1
fi

cd frontend

# 检查package.json
if [ ! -f "package.json" ]; then
    log_error "package.json文件不存在"
    kill $BACKEND_PID 2>/dev/null || true
    exit 1
fi

# 安装依赖
log_info "安装Node.js依赖包..."
npm install

if [ $? -ne 0 ]; then
    log_error "安装Node.js依赖失败"
    kill $BACKEND_PID 2>/dev/null || true
    exit 1
fi

# 设置前端环境变量
export VITE_API_URL="http://localhost:8000"
export VITE_APP_ENV="development"

# 启动前端服务
log_step "启动React+Vite前端服务..."
log_info "前端服务将在 http://localhost:3000 启动"

# 检查端口是否被占用
if lsof -i :3000 &> /dev/null; then
    log_warning "端口3000已被占用，尝试终止现有进程..."
    lsof -ti :3000 | xargs kill -9 2>/dev/null || true
    sleep 2
fi

# 启动前端服务（后台运行）
nohup npm run dev > ../logs/frontend.log 2>&1 &
FRONTEND_PID=$!

# 等待前端启动
log_info "等待前端服务启动..."
for i in {1..30}; do
    if curl -s http://localhost:3000 > /dev/null 2>&1; then
        log_success "前端服务启动成功 (PID: $FRONTEND_PID)"
        break
    fi
    if [ $i -eq 30 ]; then
        log_warning "前端服务启动超时，但可能仍在启动中"
        break
    fi
    sleep 1
done

cd ..
echo ""

# Dapr Sidecar启动（可选）
DAPR_PID=""
if [ "$DAPR_AVAILABLE" = true ] && [ "$REDIS_AVAILABLE" = true ]; then
    log_step "启动Dapr Sidecar..."

    # 检查Dapr组件配置
    if [ -d "dapr/components" ] && [ -f "dapr/config.yaml" ]; then
        log_info "启动Dapr sidecar (HTTP端口: 3500, gRPC端口: 50001)"

        # 启动Dapr sidecar
        nohup dapr run \
            --app-id annotation-backend \
            --app-port 8000 \
            --dapr-http-port 3500 \
            --dapr-grpc-port 50001 \
            --components-path ./dapr/components \
            --config ./dapr/config.yaml \
            --log-level info > logs/dapr.log 2>&1 &
        DAPR_PID=$!

        # 等待Dapr启动
        log_info "等待Dapr sidecar启动..."
        for i in {1..20}; do
            if curl -s http://localhost:3500/v1.0/healthz > /dev/null 2>&1; then
                log_success "Dapr sidecar启动成功 (PID: $DAPR_PID)"
                break
            fi
            if [ $i -eq 20 ]; then
                log_warning "Dapr sidecar启动超时"
                kill $DAPR_PID 2>/dev/null || true
                DAPR_PID=""
            fi
            sleep 1
        done
    else
        log_warning "Dapr配置文件不完整，跳过Dapr启动"
    fi
else
    if [ "$DAPR_AVAILABLE" = false ]; then
        log_info "Dapr CLI不可用，跳过Dapr启动"
    fi
    if [ "$REDIS_AVAILABLE" = false ]; then
        log_info "Redis服务不可用，跳过Dapr启动"
    fi
fi

echo ""

# 创建日志目录
mkdir -p logs

# 启动完成信息
echo -e "${GREEN}🎉 开发环境启动完成！${NC}"
echo ""
echo -e "${CYAN}📍 服务地址：${NC}"
echo -e "   ${BLUE}前端应用:${NC} http://localhost:3000"
echo -e "   ${BLUE}后端API:${NC}  http://localhost:8000"
echo -e "   ${BLUE}API文档:${NC}  http://localhost:8000/docs"
echo -e "   ${BLUE}数据库:${NC}   Supabase (远程)"
if [ "$REDIS_AVAILABLE" = true ]; then
    echo -e "   ${BLUE}Redis:${NC}    localhost:6379"
fi
if [ ! -z "$DAPR_PID" ]; then
    echo -e "   ${BLUE}Dapr HTTP:${NC} http://localhost:3500"
fi
echo ""
echo -e "${CYAN}📋 进程信息：${NC}"
echo -e "   ${BLUE}后端PID:${NC} $BACKEND_PID"
echo -e "   ${BLUE}前端PID:${NC} $FRONTEND_PID"
if [ ! -z "$DAPR_PID" ]; then
    echo -e "   ${BLUE}Dapr PID:${NC} $DAPR_PID"
fi
echo ""
echo -e "${CYAN}📝 日志文件：${NC}"
echo -e "   ${BLUE}后端日志:${NC} logs/backend.log"
echo -e "   ${BLUE}前端日志:${NC} logs/frontend.log"
if [ ! -z "$DAPR_PID" ]; then
    echo -e "   ${BLUE}Dapr日志:${NC} logs/dapr.log"
fi
echo ""
echo -e "${YELLOW}🛑 停止服务请按 Ctrl+C${NC}"
echo ""

# 清理函数
cleanup() {
    echo ""
    log_info "正在停止服务..."

    # 停止所有服务
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
        log_info "后端服务已停止"
    fi

    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
        log_info "前端服务已停止"
    fi

    if [ ! -z "$DAPR_PID" ]; then
        kill $DAPR_PID 2>/dev/null || true
        log_info "Dapr sidecar已停止"
    fi

    # 清理可能的僵尸进程
    pkill -f "uvicorn app.main:app" 2>/dev/null || true
    pkill -f "npm run dev" 2>/dev/null || true
    pkill -f "dapr run" 2>/dev/null || true

    log_success "所有服务已停止"
    exit 0
}

# 设置信号处理
trap cleanup INT TERM

# 保持脚本运行，等待用户中断
while true; do
    sleep 1
done
