{"name": "annotation-frontend", "private": true, "version": "0.1.0", "type": "module", "description": "自适应学习数据标注系统前端应用", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:e2e": "playwright test"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-context-menu": "^2.1.5", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-form": "^0.0.3", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@tanstack/react-query": "^5.8.4", "@types/d3": "^7.4.3", "@types/react-transition-group": "^4.4.10", "@types/three": "^0.158.3", "@types/vis": "^4.21.24", "axios": "^1.6.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "d3": "^7.8.5", "date-fns": "^2.30.0", "framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-force-graph": "^1.44.4", "react-force-graph-2d": "^1.25.4", "react-force-graph-3d": "^1.24.4", "react-hook-form": "^7.60.0", "react-markdown": "^9.0.1", "react-router-dom": "^6.20.1", "react-spring": "^9.7.3", "react-syntax-highlighter": "^15.5.0", "react-transition-group": "^4.4.5", "recharts": "^2.8.0", "sonner": "^2.0.6", "tailwind-merge": "^2.6.0", "three": "^0.158.0", "vis-data": "^7.1.9", "vis-network": "^9.1.9", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@playwright/test": "^1.40.0", "@tanstack/react-query-devtools": "^5.8.4", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/react-syntax-highlighter": "^15.5.10", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "@vitest/coverage-v8": "^1.0.0", "@vitest/ui": "^1.0.0", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "jsdom": "^23.0.1", "postcss": "^8.4.32", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "tailwindcss": "^3.3.6", "tailwindcss-animate": "^1.0.7", "typescript": "^5.2.2", "vite": "^5.0.0", "vitest": "^1.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}