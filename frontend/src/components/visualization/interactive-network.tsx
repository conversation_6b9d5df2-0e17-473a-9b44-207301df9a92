/**
 * 交互式网络图组件
 */

import React, { useRef, useEffect, useState, useCallback } from 'react'
import { Network } from 'vis-network'
import { DataSet } from 'vis-data'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON>lider } from '@/components/ui/slider'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  ZoomIn, 
  ZoomOut, 
  RotateCcw, 
  Download, 
  Settings,
  Play,
  Pause,
  Filter,
  Layout,
  Maximize
} from 'lucide-react'

interface NetworkNode {
  id: string
  label: string
  group: string
  level: number
  size: number
  color?: string
  shape?: string
  physics?: boolean
  fixed?: boolean
  x?: number
  y?: number
  metadata?: Record<string, any>
}

interface NetworkEdge {
  id: string
  from: string
  to: string
  label?: string
  weight: number
  color?: string
  width?: number
  arrows?: string
  dashes?: boolean
  physics?: boolean
  metadata?: Record<string, any>
}

interface InteractiveNetworkProps {
  nodes: NetworkNode[]
  edges: NetworkEdge[]
  width?: number
  height?: number
  onNodeClick?: (node: NetworkNode) => void
  onEdgeClick?: (edge: NetworkEdge) => void
  onNodeHover?: (node: NetworkNode | null) => void
  onSelection?: (selection: { nodes: string[]; edges: string[] }) => void
  layout?: string
  physics?: boolean
  clustering?: boolean
}

const layoutOptions = {
  hierarchical: {
    enabled: true,
    direction: 'UD',
    sortMethod: 'directed',
    levelSeparation: 150,
    nodeSpacing: 100
  },
  force: {
    enabled: false
  },
  random: {
    enabled: false
  }
}

const physicsOptions = {
  enabled: true,
  barnesHut: {
    gravitationalConstant: -2000,
    centralGravity: 0.3,
    springLength: 95,
    springConstant: 0.04,
    damping: 0.09,
    avoidOverlap: 0.1
  },
  maxVelocity: 50,
  minVelocity: 0.1,
  solver: 'barnesHut',
  stabilization: {
    enabled: true,
    iterations: 1000,
    updateInterval: 100,
    onlyDynamicEdges: false,
    fit: true
  },
  timestep: 0.5,
  adaptiveTimestep: true
}

export function InteractiveNetwork({
  nodes,
  edges,
  width = 800,
  height = 600,
  onNodeClick,
  onEdgeClick,
  onNodeHover,
  onSelection,
  layout = 'force',
  physics = true,
  clustering = false
}: InteractiveNetworkProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const networkRef = useRef<Network | null>(null)
  const [selectedNodes, setSelectedNodes] = useState<string[]>([])
  const [selectedEdges, setSelectedEdges] = useState<string[]>([])
  const [isStabilizing, setIsStabilizing] = useState(false)
  const [currentLayout, setCurrentLayout] = useState(layout)
  const [physicsEnabled, setPhysicsEnabled] = useState(physics)
  const [clusteringEnabled, setClusteringEnabled] = useState(clustering)
  const [nodeSize, setNodeSize] = useState([20])
  const [edgeWidth, setEdgeWidth] = useState([2])
  const [springLength, setSpringLength] = useState([95])
  const [repulsion, setRepulsion] = useState([2000])

  const initializeNetwork = useCallback(() => {
    if (!containerRef.current) return

    // 准备数据
    const nodeDataSet = new DataSet(nodes.map(node => ({
      ...node,
      size: node.size * nodeSize[0] / 20,
      font: { size: 14, color: '#333' },
      borderWidth: 2,
      shadow: true
    })))

    const edgeDataSet = new DataSet(edges.map(edge => ({
      ...edge,
      width: edge.width ? edge.width * edgeWidth[0] / 2 : edgeWidth[0],
      smooth: { type: 'continuous' },
      shadow: true
    })))

    // 网络选项
    const options = {
      layout: currentLayout === 'hierarchical' ? layoutOptions.hierarchical : layoutOptions.force,
      physics: physicsEnabled ? {
        ...physicsOptions,
        barnesHut: {
          ...physicsOptions.barnesHut,
          springLength: springLength[0],
          gravitationalConstant: -repulsion[0]
        }
      } : { enabled: false },
      interaction: {
        dragNodes: true,
        dragView: true,
        zoomView: true,
        selectConnectedEdges: true,
        hover: true,
        hoverConnectedEdges: true,
        keyboard: {
          enabled: true,
          speed: { x: 10, y: 10, zoom: 0.02 },
          bindToWindow: false
        }
      },
      manipulation: {
        enabled: false
      },
      nodes: {
        shape: 'dot',
        scaling: {
          min: 10,
          max: 30
        },
        font: {
          size: 12,
          face: 'Tahoma'
        }
      },
      edges: {
        width: 0.15,
        color: { inherit: 'from' },
        smooth: {
          type: 'continuous'
        }
      },
      groups: {
        knowledge_point: {
          color: { background: '#3b82f6', border: '#1d4ed8' },
          shape: 'circle'
        },
        question: {
          color: { background: '#10b981', border: '#047857' },
          shape: 'square'
        },
        concept: {
          color: { background: '#f59e0b', border: '#d97706' },
          shape: 'triangle'
        }
      }
    }

    // 创建网络
    const network = new Network(
      containerRef.current,
      { nodes: nodeDataSet, edges: edgeDataSet },
      options
    )

    networkRef.current = network

    // 事件监听
    network.on('click', (params) => {
      if (params.nodes.length > 0) {
        const nodeId = params.nodes[0]
        const node = nodes.find(n => n.id === nodeId)
        if (node && onNodeClick) {
          onNodeClick(node)
        }
      } else if (params.edges.length > 0) {
        const edgeId = params.edges[0]
        const edge = edges.find(e => e.id === edgeId)
        if (edge && onEdgeClick) {
          onEdgeClick(edge)
        }
      }
    })

    network.on('hoverNode', (params) => {
      const node = nodes.find(n => n.id === params.node)
      if (node && onNodeHover) {
        onNodeHover(node)
      }
    })

    network.on('blurNode', () => {
      if (onNodeHover) {
        onNodeHover(null)
      }
    })

    network.on('select', (params) => {
      setSelectedNodes(params.nodes)
      setSelectedEdges(params.edges)
      if (onSelection) {
        onSelection({ nodes: params.nodes, edges: params.edges })
      }
    })

    network.on('stabilizationProgress', (params) => {
      setIsStabilizing(true)
    })

    network.on('stabilizationIterationsDone', () => {
      setIsStabilizing(false)
    })

    // 聚类功能
    if (clusteringEnabled) {
      const clusterOptionsByData = {
        joinCondition: (childOptions: any) => {
          return childOptions.group === 'knowledge_point'
        },
        clusterNodeProperties: {
          id: 'cluster:knowledge_points',
          borderWidth: 3,
          shape: 'database',
          color: '#ff9999',
          label: '知识点集群'
        }
      }
      network.cluster(clusterOptionsByData)
    }

  }, [
    nodes, 
    edges, 
    currentLayout, 
    physicsEnabled, 
    clusteringEnabled,
    nodeSize,
    edgeWidth,
    springLength,
    repulsion,
    onNodeClick,
    onEdgeClick,
    onNodeHover,
    onSelection
  ])

  useEffect(() => {
    initializeNetwork()
    
    return () => {
      if (networkRef.current) {
        networkRef.current.destroy()
      }
    }
  }, [initializeNetwork])

  const handleZoomIn = () => {
    if (networkRef.current) {
      const scale = networkRef.current.getScale()
      networkRef.current.moveTo({ scale: scale * 1.2 })
    }
  }

  const handleZoomOut = () => {
    if (networkRef.current) {
      const scale = networkRef.current.getScale()
      networkRef.current.moveTo({ scale: scale * 0.8 })
    }
  }

  const handleFit = () => {
    if (networkRef.current) {
      networkRef.current.fit()
    }
  }

  const handleStabilize = () => {
    if (networkRef.current) {
      networkRef.current.stabilize()
    }
  }

  const handleExport = () => {
    if (networkRef.current) {
      const canvas = networkRef.current.getCanvas()
      const link = document.createElement('a')
      link.download = 'network-graph.png'
      link.href = canvas.toDataURL()
      link.click()
    }
  }

  const handleLayoutChange = (newLayout: string) => {
    setCurrentLayout(newLayout)
  }

  const handleCluster = () => {
    if (networkRef.current && selectedNodes.length > 1) {
      const clusterOptions = {
        joinCondition: (nodeOptions: any) => {
          return selectedNodes.includes(nodeOptions.id)
        },
        clusterNodeProperties: {
          id: `cluster:${Date.now()}`,
          borderWidth: 3,
          shape: 'database',
          color: '#ff9999',
          label: `集群 (${selectedNodes.length})`
        }
      }
      networkRef.current.cluster(clusterOptions)
    }
  }

  const handleOpenCluster = () => {
    if (networkRef.current && selectedNodes.length === 1) {
      const nodeId = selectedNodes[0]
      if (nodeId.startsWith('cluster:')) {
        networkRef.current.openCluster(nodeId)
      }
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>交互式网络图</CardTitle>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={handleZoomIn}>
              <ZoomIn className="w-4 h-4" />
            </Button>
            <Button variant="outline" size="sm" onClick={handleZoomOut}>
              <ZoomOut className="w-4 h-4" />
            </Button>
            <Button variant="outline" size="sm" onClick={handleFit}>
              <RotateCcw className="w-4 h-4" />
            </Button>
            <Button variant="outline" size="sm" onClick={handleStabilize}>
              <Play className="w-4 h-4" />
            </Button>
            <Button variant="outline" size="sm" onClick={handleExport}>
              <Download className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* 控制面板 */}
          <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium w-20">布局:</span>
                <Select value={currentLayout} onValueChange={handleLayoutChange}>
                  <SelectTrigger className="flex-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="force">力导向</SelectItem>
                    <SelectItem value="hierarchical">层次布局</SelectItem>
                    <SelectItem value="random">随机布局</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium w-20">节点大小:</span>
                <Slider
                  value={nodeSize}
                  onValueChange={setNodeSize}
                  max={50}
                  min={5}
                  step={5}
                  className="flex-1"
                />
                <span className="text-sm text-gray-600 w-8">{nodeSize[0]}</span>
              </div>
              
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium w-20">边宽度:</span>
                <Slider
                  value={edgeWidth}
                  onValueChange={setEdgeWidth}
                  max={10}
                  min={1}
                  step={1}
                  className="flex-1"
                />
                <span className="text-sm text-gray-600 w-8">{edgeWidth[0]}</span>
              </div>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium w-20">弹簧长度:</span>
                <Slider
                  value={springLength}
                  onValueChange={setSpringLength}
                  max={200}
                  min={50}
                  step={5}
                  className="flex-1"
                />
                <span className="text-sm text-gray-600 w-8">{springLength[0]}</span>
              </div>
              
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium w-20">排斥力:</span>
                <Slider
                  value={repulsion}
                  onValueChange={setRepulsion}
                  max={5000}
                  min={500}
                  step={100}
                  className="flex-1"
                />
                <span className="text-sm text-gray-600 w-8">{repulsion[0]}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">物理引擎</span>
                <Switch 
                  checked={physicsEnabled} 
                  onCheckedChange={setPhysicsEnabled}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">聚类模式</span>
                <Switch 
                  checked={clusteringEnabled} 
                  onCheckedChange={setClusteringEnabled}
                />
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          {selectedNodes.length > 0 && (
            <div className="flex items-center gap-2 p-3 bg-blue-50 rounded-lg">
              <span className="text-sm font-medium">
                已选择 {selectedNodes.length} 个节点, {selectedEdges.length} 条边
              </span>
              {selectedNodes.length > 1 && (
                <Button size="sm" onClick={handleCluster}>
                  <Filter className="w-4 h-4 mr-1" />
                  创建集群
                </Button>
              )}
              {selectedNodes.length === 1 && selectedNodes[0].startsWith('cluster:') && (
                <Button size="sm" onClick={handleOpenCluster}>
                  <Maximize className="w-4 h-4 mr-1" />
                  展开集群
                </Button>
              )}
            </div>
          )}

          {/* 状态指示器 */}
          {isStabilizing && (
            <div className="flex items-center gap-2 p-3 bg-yellow-50 rounded-lg">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-600"></div>
              <span className="text-sm text-yellow-800">正在稳定化布局...</span>
            </div>
          )}

          {/* 网络图容器 */}
          <div className="border rounded-lg overflow-hidden">
            <div
              ref={containerRef}
              style={{ width, height }}
              className="bg-white"
            />
          </div>

          {/* 操作提示 */}
          <div className="text-xs text-gray-500 p-2 bg-gray-50 rounded">
            <p>• 拖拽节点移动位置</p>
            <p>• 鼠标滚轮缩放视图</p>
            <p>• 点击节点或边查看详情</p>
            <p>• Ctrl+点击多选节点</p>
            <p>• 双击节点固定位置</p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
