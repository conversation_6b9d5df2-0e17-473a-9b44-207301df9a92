/**
 * 知识图谱可视化组件
 */

import React, { useRef, useEffect, useState, useCallback } from 'react'
import * as d3 from 'd3'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Slider } from '@/components/ui/slider'
import { 
  ZoomIn, 
  ZoomOut, 
  RotateCcw, 
  Download, 
  Settings,
  Play,
  Pause,
  Square
} from 'lucide-react'

interface KnowledgeNode {
  id: string
  name: string
  type: 'knowledge_point' | 'question' | 'concept'
  level: number
  weight: number
  group: string
  description?: string
  metadata?: Record<string, any>
}

interface KnowledgeLink {
  source: string
  target: string
  type: 'prerequisite' | 'related' | 'contains' | 'similar'
  weight: number
  strength: number
  metadata?: Record<string, any>
}

interface KnowledgeGraphData {
  nodes: KnowledgeNode[]
  links: KnowledgeLink[]
}

interface KnowledgeGraphProps {
  data: KnowledgeGraphData
  width?: number
  height?: number
  onNodeClick?: (node: KnowledgeNode) => void
  onLinkClick?: (link: KnowledgeLink) => void
  onNodeHover?: (node: KnowledgeNode | null) => void
  interactive?: boolean
  showLabels?: boolean
  showLegend?: boolean
}

const nodeTypeColors = {
  knowledge_point: '#3b82f6',
  question: '#10b981',
  concept: '#f59e0b'
}

const linkTypeColors = {
  prerequisite: '#ef4444',
  related: '#8b5cf6',
  contains: '#06b6d4',
  similar: '#84cc16'
}

export function KnowledgeGraph({
  data,
  width = 800,
  height = 600,
  onNodeClick,
  onLinkClick,
  onNodeHover,
  interactive = true,
  showLabels = true,
  showLegend = true
}: KnowledgeGraphProps) {
  const svgRef = useRef<SVGSVGElement>(null)
  const [simulation, setSimulation] = useState<d3.Simulation<KnowledgeNode, KnowledgeLink> | null>(null)
  const [isPlaying, setIsPlaying] = useState(true)
  const [selectedNode, setSelectedNode] = useState<KnowledgeNode | null>(null)
  const [zoomLevel, setZoomLevel] = useState(1)
  const [linkDistance, setLinkDistance] = useState([100])
  const [chargeStrength, setChargeStrength] = useState([-300])

  const initializeGraph = useCallback(() => {
    if (!svgRef.current || !data.nodes.length) return

    const svg = d3.select(svgRef.current)
    svg.selectAll('*').remove()

    // 创建缩放行为
    const zoom = d3.zoom<SVGSVGElement, unknown>()
      .scaleExtent([0.1, 4])
      .on('zoom', (event) => {
        const { transform } = event
        setZoomLevel(transform.k)
        svg.select('.graph-container').attr('transform', transform)
      })

    svg.call(zoom)

    // 创建容器组
    const container = svg.append('g').attr('class', 'graph-container')

    // 创建箭头标记
    const defs = svg.append('defs')
    
    Object.entries(linkTypeColors).forEach(([type, color]) => {
      defs.append('marker')
        .attr('id', `arrow-${type}`)
        .attr('viewBox', '0 -5 10 10')
        .attr('refX', 20)
        .attr('refY', 0)
        .attr('markerWidth', 6)
        .attr('markerHeight', 6)
        .attr('orient', 'auto')
        .append('path')
        .attr('d', 'M0,-5L10,0L0,5')
        .attr('fill', color)
    })

    // 创建力导向布局
    const sim = d3.forceSimulation<KnowledgeNode>(data.nodes)
      .force('link', d3.forceLink<KnowledgeNode, KnowledgeLink>(data.links)
        .id(d => d.id)
        .distance(linkDistance[0])
        .strength(d => d.strength))
      .force('charge', d3.forceManyBody().strength(chargeStrength[0]))
      .force('center', d3.forceCenter(width / 2, height / 2))
      .force('collision', d3.forceCollide().radius(d => Math.sqrt(d.weight) * 5 + 5))

    setSimulation(sim)

    // 绘制连接线
    const links = container.selectAll('.link')
      .data(data.links)
      .enter().append('line')
      .attr('class', 'link')
      .attr('stroke', d => linkTypeColors[d.type])
      .attr('stroke-width', d => Math.sqrt(d.weight) * 2)
      .attr('stroke-opacity', 0.6)
      .attr('marker-end', d => `url(#arrow-${d.type})`)
      .style('cursor', interactive ? 'pointer' : 'default')

    if (interactive && onLinkClick) {
      links.on('click', (event, d) => {
        event.stopPropagation()
        onLinkClick(d)
      })
    }

    // 绘制节点
    const nodes = container.selectAll('.node')
      .data(data.nodes)
      .enter().append('g')
      .attr('class', 'node')
      .style('cursor', interactive ? 'pointer' : 'default')

    // 节点圆圈
    nodes.append('circle')
      .attr('r', d => Math.sqrt(d.weight) * 5 + 5)
      .attr('fill', d => nodeTypeColors[d.type])
      .attr('stroke', '#fff')
      .attr('stroke-width', 2)

    // 节点标签
    if (showLabels) {
      nodes.append('text')
        .attr('dx', d => Math.sqrt(d.weight) * 5 + 8)
        .attr('dy', '.35em')
        .style('font-size', '12px')
        .style('font-family', 'Arial, sans-serif')
        .text(d => d.name)
    }

    // 交互事件
    if (interactive) {
      nodes
        .on('click', (event, d) => {
          event.stopPropagation()
          setSelectedNode(d)
          onNodeClick?.(d)
        })
        .on('mouseover', (event, d) => {
          onNodeHover?.(d)
          // 高亮相关节点和连接
          highlightConnectedNodes(d.id)
        })
        .on('mouseout', () => {
          onNodeHover?.(null)
          resetHighlight()
        })

      // 拖拽行为
      const drag = d3.drag<SVGGElement, KnowledgeNode>()
        .on('start', (event, d) => {
          if (!event.active) sim.alphaTarget(0.3).restart()
          d.fx = d.x
          d.fy = d.y
        })
        .on('drag', (event, d) => {
          d.fx = event.x
          d.fy = event.y
        })
        .on('end', (event, d) => {
          if (!event.active) sim.alphaTarget(0)
          d.fx = null
          d.fy = null
        })

      nodes.call(drag)
    }

    // 高亮相关节点
    function highlightConnectedNodes(nodeId: string) {
      const connectedNodeIds = new Set<string>()
      connectedNodeIds.add(nodeId)

      data.links.forEach(link => {
        if (link.source === nodeId) connectedNodeIds.add(link.target)
        if (link.target === nodeId) connectedNodeIds.add(link.source)
      })

      nodes.style('opacity', d => connectedNodeIds.has(d.id) ? 1 : 0.3)
      links.style('opacity', d => 
        d.source === nodeId || d.target === nodeId ? 1 : 0.1
      )
    }

    // 重置高亮
    function resetHighlight() {
      nodes.style('opacity', 1)
      links.style('opacity', 0.6)
    }

    // 更新位置
    sim.on('tick', () => {
      links
        .attr('x1', d => (d.source as any).x)
        .attr('y1', d => (d.source as any).y)
        .attr('x2', d => (d.target as any).x)
        .attr('y2', d => (d.target as any).y)

      nodes.attr('transform', d => `translate(${d.x},${d.y})`)
    })

  }, [data, width, height, linkDistance, chargeStrength, interactive, showLabels, onNodeClick, onLinkClick, onNodeHover])

  useEffect(() => {
    initializeGraph()
  }, [initializeGraph])

  useEffect(() => {
    if (simulation) {
      if (isPlaying) {
        simulation.restart()
      } else {
        simulation.stop()
      }
    }
  }, [isPlaying, simulation])

  const handleZoomIn = () => {
    const svg = d3.select(svgRef.current)
    svg.transition().call(
      d3.zoom<SVGSVGElement, unknown>().scaleBy as any,
      1.5
    )
  }

  const handleZoomOut = () => {
    const svg = d3.select(svgRef.current)
    svg.transition().call(
      d3.zoom<SVGSVGElement, unknown>().scaleBy as any,
      1 / 1.5
    )
  }

  const handleReset = () => {
    const svg = d3.select(svgRef.current)
    svg.transition().call(
      d3.zoom<SVGSVGElement, unknown>().transform as any,
      d3.zoomIdentity
    )
    setZoomLevel(1)
    if (simulation) {
      simulation.alpha(1).restart()
    }
  }

  const handleExport = () => {
    if (!svgRef.current) return
    
    const svgData = new XMLSerializer().serializeToString(svgRef.current)
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()
    
    canvas.width = width
    canvas.height = height
    
    img.onload = () => {
      ctx?.drawImage(img, 0, 0)
      const link = document.createElement('a')
      link.download = 'knowledge-graph.png'
      link.href = canvas.toDataURL()
      link.click()
    }
    
    img.src = 'data:image/svg+xml;base64,' + btoa(svgData)
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>知识图谱</CardTitle>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={handleZoomIn}>
              <ZoomIn className="w-4 h-4" />
            </Button>
            <Button variant="outline" size="sm" onClick={handleZoomOut}>
              <ZoomOut className="w-4 h-4" />
            </Button>
            <Button variant="outline" size="sm" onClick={handleReset}>
              <RotateCcw className="w-4 h-4" />
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => setIsPlaying(!isPlaying)}
            >
              {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
            </Button>
            <Button variant="outline" size="sm" onClick={handleExport}>
              <Download className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* 控制面板 */}
          <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">连接距离:</span>
              <Slider
                value={linkDistance}
                onValueChange={setLinkDistance}
                max={200}
                min={50}
                step={10}
                className="w-24"
              />
              <span className="text-sm text-gray-600">{linkDistance[0]}</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">排斥力:</span>
              <Slider
                value={chargeStrength.map(v => -v)}
                onValueChange={(value) => setChargeStrength(value.map(v => -v))}
                max={500}
                min={100}
                step={50}
                className="w-24"
              />
              <span className="text-sm text-gray-600">{-chargeStrength[0]}</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">缩放:</span>
              <span className="text-sm text-gray-600">{(zoomLevel * 100).toFixed(0)}%</span>
            </div>
          </div>

          {/* 图例 */}
          {showLegend && (
            <div className="flex items-center gap-6 p-4 bg-gray-50 rounded-lg">
              <div>
                <h4 className="text-sm font-medium mb-2">节点类型</h4>
                <div className="flex gap-3">
                  {Object.entries(nodeTypeColors).map(([type, color]) => (
                    <div key={type} className="flex items-center gap-1">
                      <div 
                        className="w-3 h-3 rounded-full" 
                        style={{ backgroundColor: color }}
                      />
                      <span className="text-xs">{type}</span>
                    </div>
                  ))}
                </div>
              </div>
              <div>
                <h4 className="text-sm font-medium mb-2">连接类型</h4>
                <div className="flex gap-3">
                  {Object.entries(linkTypeColors).map(([type, color]) => (
                    <div key={type} className="flex items-center gap-1">
                      <div 
                        className="w-4 h-0.5" 
                        style={{ backgroundColor: color }}
                      />
                      <span className="text-xs">{type}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* 选中节点信息 */}
          {selectedNode && (
            <div className="p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium mb-2">选中节点: {selectedNode.name}</h4>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div>类型: <Badge variant="outline">{selectedNode.type}</Badge></div>
                <div>层级: {selectedNode.level}</div>
                <div>权重: {selectedNode.weight}</div>
                <div>分组: {selectedNode.group}</div>
              </div>
              {selectedNode.description && (
                <p className="text-sm text-gray-600 mt-2">{selectedNode.description}</p>
              )}
            </div>
          )}

          {/* SVG 图形 */}
          <div className="border rounded-lg overflow-hidden">
            <svg
              ref={svgRef}
              width={width}
              height={height}
              style={{ background: '#fafafa' }}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
