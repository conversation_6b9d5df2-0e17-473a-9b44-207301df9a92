/**
 * 动态图表组件
 */

import React, { useState, useEffect, useRef } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Slider } from '@/components/ui/slider'
import { 
  BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer,
  LineChart, Line, PieChart, Pie, Cell, AreaChart, Area,
  ScatterChart, Scatter, RadialBarChart, RadialBar
} from 'recharts'
import { motion, AnimatePresence } from 'framer-motion'
import { useSpring, animated, useTransition } from 'react-spring'
import { 
  Play, 
  Pause, 
  RotateCcw, 
  SkipForward,
  SkipBack,
  Settings,
  TrendingUp,
  Activity,
  Zap
} from 'lucide-react'

interface AnimatedDataPoint {
  id: string
  name: string
  value: number
  color: string
  timestamp: number
  metadata?: Record<string, any>
}

interface AnimatedChartProps {
  data: AnimatedDataPoint[][]
  chartType: 'bar' | 'line' | 'pie' | 'area' | 'scatter' | 'radial'
  width?: number
  height?: number
  animationDuration?: number
  autoPlay?: boolean
  showControls?: boolean
  onDataChange?: (currentData: AnimatedDataPoint[], frameIndex: number) => void
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D']

export function AnimatedCharts({
  data,
  chartType = 'bar',
  width = 600,
  height = 400,
  animationDuration = 1000,
  autoPlay = false,
  showControls = true,
  onDataChange
}: AnimatedChartProps) {
  const [currentFrame, setCurrentFrame] = useState(0)
  const [isPlaying, setIsPlaying] = useState(autoPlay)
  const [speed, setSpeed] = useState([1])
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  const currentData = data[currentFrame] || []
  const maxValue = Math.max(...data.flat().map(d => d.value))

  // 动画配置
  const springProps = useSpring({
    from: { opacity: 0, transform: 'scale(0.8)' },
    to: { opacity: 1, transform: 'scale(1)' },
    config: { duration: animationDuration / 2 }
  })

  // 数据过渡动画
  const transitions = useTransition(currentData, {
    from: { opacity: 0, transform: 'translateY(20px)' },
    enter: { opacity: 1, transform: 'translateY(0px)' },
    leave: { opacity: 0, transform: 'translateY(-20px)' },
    config: { duration: animationDuration / 3 }
  })

  useEffect(() => {
    if (isPlaying && data.length > 1) {
      intervalRef.current = setInterval(() => {
        setCurrentFrame(prev => {
          const next = (prev + 1) % data.length
          onDataChange?.(data[next], next)
          return next
        })
      }, animationDuration / speed[0])
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [isPlaying, data.length, animationDuration, speed, onDataChange])

  const handlePlay = () => {
    setIsPlaying(!isPlaying)
  }

  const handleReset = () => {
    setCurrentFrame(0)
    setIsPlaying(false)
  }

  const handleNext = () => {
    setCurrentFrame(prev => Math.min(prev + 1, data.length - 1))
  }

  const handlePrev = () => {
    setCurrentFrame(prev => Math.max(prev - 1, 0))
  }

  const handleFrameChange = (frame: number) => {
    setCurrentFrame(frame)
    onDataChange?.(data[frame], frame)
  }

  // 自定义动画组件
  const AnimatedBar = ({ data: barData }: { data: any }) => {
    const animatedProps = useSpring({
      height: (barData.value / maxValue) * height * 0.7,
      config: { tension: 300, friction: 30 }
    })

    return (
      <animated.div
        style={{
          ...animatedProps,
          backgroundColor: barData.color,
          width: '40px',
          margin: '0 5px',
          borderRadius: '4px 4px 0 0'
        }}
      />
    )
  }

  const AnimatedNumber = ({ value }: { value: number }) => {
    const { number } = useSpring({
      from: { number: 0 },
      number: value,
      delay: 200,
      config: { mass: 1, tension: 20, friction: 10 }
    })

    return (
      <animated.span>
        {number.to(n => n.toFixed(0))}
      </animated.span>
    )
  }

  const renderChart = () => {
    switch (chartType) {
      case 'bar':
        return (
          <ResponsiveContainer width="100%" height={height}>
            <BarChart data={currentData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="value" fill="#8884d8">
                {currentData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        )

      case 'line':
        return (
          <ResponsiveContainer width="100%" height={height}>
            <LineChart data={currentData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Line 
                type="monotone" 
                dataKey="value" 
                stroke="#8884d8" 
                strokeWidth={3}
                dot={{ r: 6 }}
                animationDuration={animationDuration}
              />
            </LineChart>
          </ResponsiveContainer>
        )

      case 'pie':
        return (
          <ResponsiveContainer width="100%" height={height}>
            <PieChart>
              <Pie
                data={currentData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
                animationBegin={0}
                animationDuration={animationDuration}
              >
                {currentData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        )

      case 'area':
        return (
          <ResponsiveContainer width="100%" height={height}>
            <AreaChart data={currentData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Area 
                type="monotone" 
                dataKey="value" 
                stroke="#8884d8" 
                fill="#8884d8" 
                fillOpacity={0.6}
                animationDuration={animationDuration}
              />
            </AreaChart>
          </ResponsiveContainer>
        )

      case 'scatter':
        return (
          <ResponsiveContainer width="100%" height={height}>
            <ScatterChart data={currentData}>
              <CartesianGrid />
              <XAxis dataKey="name" />
              <YAxis dataKey="value" />
              <Tooltip cursor={{ strokeDasharray: '3 3' }} />
              <Scatter 
                name="数据点" 
                data={currentData} 
                fill="#8884d8"
              />
            </ScatterChart>
          </ResponsiveContainer>
        )

      case 'radial':
        return (
          <ResponsiveContainer width="100%" height={height}>
            <RadialBarChart 
              cx="50%" 
              cy="50%" 
              innerRadius="10%" 
              outerRadius="80%" 
              data={currentData}
            >
              <RadialBar 
                dataKey="value" 
                cornerRadius={10} 
                fill="#8884d8"
                animationDuration={animationDuration}
              />
              <Tooltip />
            </RadialBarChart>
          </ResponsiveContainer>
        )

      default:
        return null
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Activity className="w-5 h-5" />
            动态图表
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant="outline">
              帧 {currentFrame + 1} / {data.length}
            </Badge>
            <Badge variant="outline" className="text-green-600">
              <Zap className="w-3 h-3 mr-1" />
              {speed[0]}x
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* 控制面板 */}
          {showControls && (
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" onClick={handlePrev}>
                  <SkipBack className="w-4 h-4" />
                </Button>
                <Button variant="outline" size="sm" onClick={handlePlay}>
                  {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                </Button>
                <Button variant="outline" size="sm" onClick={handleNext}>
                  <SkipForward className="w-4 h-4" />
                </Button>
                <Button variant="outline" size="sm" onClick={handleReset}>
                  <RotateCcw className="w-4 h-4" />
                </Button>
              </div>
              
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">速度:</span>
                  <Slider
                    value={speed}
                    onValueChange={setSpeed}
                    max={5}
                    min={0.5}
                    step={0.5}
                    className="w-20"
                  />
                  <span className="text-sm text-gray-600">{speed[0]}x</span>
                </div>
              </div>
            </div>
          )}

          {/* 进度条 */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm text-gray-600">
              <span>进度</span>
              <span>{Math.round((currentFrame / (data.length - 1)) * 100)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <motion.div
                className="bg-blue-600 h-2 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${(currentFrame / (data.length - 1)) * 100}%` }}
                transition={{ duration: 0.3 }}
              />
            </div>
            <input
              type="range"
              min={0}
              max={data.length - 1}
              value={currentFrame}
              onChange={(e) => handleFrameChange(parseInt(e.target.value))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
          </div>

          {/* 当前数据统计 */}
          <div className="grid grid-cols-3 gap-4 p-4 bg-blue-50 rounded-lg">
            <div className="text-center">
              <p className="text-sm text-gray-600">数据点数量</p>
              <p className="text-2xl font-bold">
                <AnimatedNumber value={currentData.length} />
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-600">最大值</p>
              <p className="text-2xl font-bold">
                <AnimatedNumber value={Math.max(...currentData.map(d => d.value))} />
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-600">平均值</p>
              <p className="text-2xl font-bold">
                <AnimatedNumber 
                  value={currentData.reduce((sum, d) => sum + d.value, 0) / currentData.length || 0} 
                />
              </p>
            </div>
          </div>

          {/* 图表容器 */}
          <animated.div style={springProps} className="border rounded-lg overflow-hidden">
            {renderChart()}
          </animated.div>

          {/* 数据列表 */}
          <div className="max-h-40 overflow-y-auto">
            <AnimatePresence>
              {transitions((style, item) => (
                <animated.div
                  style={style}
                  className="flex items-center justify-between p-2 border-b last:border-b-0"
                >
                  <div className="flex items-center gap-2">
                    <div 
                      className="w-3 h-3 rounded-full" 
                      style={{ backgroundColor: item.color }}
                    />
                    <span className="text-sm font-medium">{item.name}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-600">
                      <AnimatedNumber value={item.value} />
                    </span>
                    <TrendingUp className="w-4 h-4 text-green-500" />
                  </div>
                </animated.div>
              ))}
            </AnimatePresence>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
