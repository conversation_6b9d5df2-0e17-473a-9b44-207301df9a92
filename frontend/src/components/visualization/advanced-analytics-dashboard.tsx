/**
 * 高级数据分析仪表板组件
 */

import React, { useState, useEffect, useMemo } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer,
  LineChart, Line, PieChart, Pie, Cell, Scatter,
  RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar,
  Treemap, Sankey, FunnelChart, Funnel, LabelList,
  ComposedChart, Area, AreaChart, ScatterChart
} from 'recharts'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  TrendingUp, 
  TrendingDown, 
  Bar<PERSON>hart3, 
  <PERSON><PERSON><PERSON> as Pie<PERSON>hartIcon,
  Activity,
  Target,
  Users,
  Brain,
  Download,
  RefreshCw,
  Filter,
  Calendar,
  Zap
} from 'lucide-react'

interface AnalyticsData {
  overview: {
    totalQuestions: number
    totalKnowledgePoints: number
    totalMappings: number
    annotationCoverage: number
    qualityScore: number
    activeUsers: number
  }
  trends: {
    date: string
    questions: number
    mappings: number
    quality: number
    users: number
  }[]
  distribution: {
    name: string
    value: number
    color: string
  }[]
  performance: {
    subject: string
    accuracy: number
    consistency: number
    completeness: number
    efficiency: number
  }[]
  heatmap: {
    x: string
    y: string
    value: number
  }[]
  funnel: {
    name: string
    value: number
    fill: string
  }[]
  network: {
    nodes: { id: string; group: number; size: number }[]
    links: { source: string; target: string; value: number }[]
  }
}

interface AdvancedAnalyticsDashboardProps {
  data: AnalyticsData
  timeRange?: string
  onTimeRangeChange?: (range: string) => void
  onExport?: (type: string) => void
  refreshData?: () => void
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D']

export function AdvancedAnalyticsDashboard({
  data,
  timeRange = '7d',
  onTimeRangeChange,
  onExport,
  refreshData
}: AdvancedAnalyticsDashboardProps) {
  const [activeTab, setActiveTab] = useState('overview')
  const [isLoading, setIsLoading] = useState(false)
  const [selectedMetric, setSelectedMetric] = useState('all')

  // 计算趋势指标
  const trendMetrics = useMemo(() => {
    if (!data.trends || data.trends.length < 2) return {}
    
    const latest = data.trends[data.trends.length - 1]
    const previous = data.trends[data.trends.length - 2]
    
    return {
      questions: ((latest.questions - previous.questions) / previous.questions * 100).toFixed(1),
      mappings: ((latest.mappings - previous.mappings) / previous.mappings * 100).toFixed(1),
      quality: ((latest.quality - previous.quality) / previous.quality * 100).toFixed(1),
      users: ((latest.users - previous.users) / previous.users * 100).toFixed(1)
    }
  }, [data.trends])

  const handleRefresh = async () => {
    setIsLoading(true)
    try {
      await refreshData?.()
    } finally {
      setIsLoading(false)
    }
  }

  const MetricCard = ({ title, value, trend, icon: Icon, color }: any) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">{title}</p>
              <p className="text-2xl font-bold">{value}</p>
              {trend && (
                <div className={`flex items-center gap-1 text-sm ${
                  parseFloat(trend) >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {parseFloat(trend) >= 0 ? (
                    <TrendingUp className="w-4 h-4" />
                  ) : (
                    <TrendingDown className="w-4 h-4" />
                  )}
                  {Math.abs(parseFloat(trend))}%
                </div>
              )}
            </div>
            <Icon className={`h-8 w-8 ${color}`} />
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border rounded-lg shadow-lg">
          <p className="font-medium">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }}>
              {entry.name}: {entry.value}
            </p>
          ))}
        </div>
      )
    }
    return null
  }

  return (
    <div className="space-y-6">
      {/* 头部控制栏 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">高级数据分析</h1>
          <p className="text-gray-600">深度洞察数据标注系统的运行状况</p>
        </div>
        <div className="flex items-center gap-3">
          <Select value={timeRange} onValueChange={onTimeRangeChange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1d">今天</SelectItem>
              <SelectItem value="7d">7天</SelectItem>
              <SelectItem value="30d">30天</SelectItem>
              <SelectItem value="90d">90天</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={handleRefresh} disabled={isLoading}>
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            刷新
          </Button>
          <Button variant="outline" onClick={() => onExport?.('pdf')}>
            <Download className="w-4 h-4 mr-2" />
            导出
          </Button>
        </div>
      </div>

      {/* 概览指标卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title="题目总数"
          value={data.overview.totalQuestions.toLocaleString()}
          trend={trendMetrics.questions}
          icon={Brain}
          color="text-blue-600"
        />
        <MetricCard
          title="知识点数量"
          value={data.overview.totalKnowledgePoints.toLocaleString()}
          trend={trendMetrics.mappings}
          icon={Target}
          color="text-green-600"
        />
        <MetricCard
          title="标注覆盖率"
          value={`${data.overview.annotationCoverage}%`}
          trend={trendMetrics.quality}
          icon={Activity}
          color="text-orange-600"
        />
        <MetricCard
          title="活跃用户"
          value={data.overview.activeUsers}
          trend={trendMetrics.users}
          icon={Users}
          color="text-purple-600"
        />
      </div>

      {/* 主要分析面板 */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="trends">趋势分析</TabsTrigger>
          <TabsTrigger value="distribution">分布分析</TabsTrigger>
          <TabsTrigger value="performance">性能分析</TabsTrigger>
          <TabsTrigger value="advanced">高级分析</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* 质量分布饼图 */}
            <Card>
              <CardHeader>
                <CardTitle>质量分布</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={data.distribution}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {data.distribution.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip content={<CustomTooltip />} />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* 性能雷达图 */}
            <Card>
              <CardHeader>
                <CardTitle>综合性能</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RadarChart data={data.performance}>
                    <PolarGrid />
                    <PolarAngleAxis dataKey="subject" />
                    <PolarRadiusAxis domain={[0, 100]} />
                    <Radar
                      name="准确性"
                      dataKey="accuracy"
                      stroke="#8884d8"
                      fill="#8884d8"
                      fillOpacity={0.3}
                    />
                    <Radar
                      name="一致性"
                      dataKey="consistency"
                      stroke="#82ca9d"
                      fill="#82ca9d"
                      fillOpacity={0.3}
                    />
                    <Tooltip content={<CustomTooltip />} />
                  </RadarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>趋势分析</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <ComposedChart data={data.trends}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis yAxisId="left" />
                  <YAxis yAxisId="right" orientation="right" />
                  <Tooltip content={<CustomTooltip />} />
                  <Area
                    yAxisId="left"
                    type="monotone"
                    dataKey="questions"
                    fill="#8884d8"
                    stroke="#8884d8"
                    fillOpacity={0.3}
                  />
                  <Bar yAxisId="left" dataKey="mappings" fill="#82ca9d" />
                  <Line
                    yAxisId="right"
                    type="monotone"
                    dataKey="quality"
                    stroke="#ff7300"
                    strokeWidth={3}
                  />
                </ComposedChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="distribution" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* 树状图 */}
            <Card>
              <CardHeader>
                <CardTitle>知识点分布</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <Treemap
                    data={data.distribution}
                    dataKey="value"
                    stroke="#fff"
                    fill="#8884d8"
                  />
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* 漏斗图 */}
            <Card>
              <CardHeader>
                <CardTitle>标注流程</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <FunnelChart>
                    <Funnel
                      dataKey="value"
                      data={data.funnel}
                      isAnimationActive
                    >
                      <LabelList position="center" fill="#fff" stroke="none" />
                    </Funnel>
                    <Tooltip content={<CustomTooltip />} />
                  </FunnelChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>性能热力图</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-10 gap-1">
                {data.heatmap.map((cell, index) => (
                  <motion.div
                    key={index}
                    className="aspect-square rounded"
                    style={{
                      backgroundColor: `rgba(59, 130, 246, ${cell.value / 100})`
                    }}
                    whileHover={{ scale: 1.1 }}
                    title={`${cell.x}-${cell.y}: ${cell.value}`}
                  />
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="advanced" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* 散点图 */}
            <Card>
              <CardHeader>
                <CardTitle>质量-效率关系</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <ScatterChart data={data.performance}>
                    <CartesianGrid />
                    <XAxis dataKey="accuracy" name="准确性" />
                    <YAxis dataKey="efficiency" name="效率" />
                    <Tooltip cursor={{ strokeDasharray: '3 3' }} />
                    <Scatter
                      name="性能指标"
                      data={data.performance}
                      fill="#8884d8"
                    />
                  </ScatterChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* 实时指标 */}
            <Card>
              <CardHeader>
                <CardTitle>实时指标</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">系统负载</span>
                    <div className="flex items-center gap-2">
                      <div className="w-20 h-2 bg-gray-200 rounded-full overflow-hidden">
                        <motion.div
                          className="h-full bg-green-500"
                          initial={{ width: 0 }}
                          animate={{ width: '75%' }}
                          transition={{ duration: 1 }}
                        />
                      </div>
                      <span className="text-sm text-gray-600">75%</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">内存使用</span>
                    <div className="flex items-center gap-2">
                      <div className="w-20 h-2 bg-gray-200 rounded-full overflow-hidden">
                        <motion.div
                          className="h-full bg-blue-500"
                          initial={{ width: 0 }}
                          animate={{ width: '60%' }}
                          transition={{ duration: 1, delay: 0.2 }}
                        />
                      </div>
                      <span className="text-sm text-gray-600">60%</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">响应时间</span>
                    <Badge variant="outline" className="text-green-600">
                      <Zap className="w-3 h-3 mr-1" />
                      120ms
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
