/**
 * 3D知识图谱可视化组件
 */

import React, { useRef, useEffect, useState, useCallback } from 'react'
import ForceGraph3D from 'react-force-graph-3d'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Slider } from '@/components/ui/slider'
import { Switch } from '@/components/ui/switch'
import { 
  RotateCcw, 
  Download, 
  Settings,
  Play,
  Pause,
  Maximize,
  Eye,
  EyeOff
} from 'lucide-react'

interface Node3D {
  id: string
  name: string
  type: 'knowledge_point' | 'question' | 'concept'
  level: number
  weight: number
  group: string
  color?: string
  size?: number
  description?: string
  x?: number
  y?: number
  z?: number
}

interface Link3D {
  source: string | Node3D
  target: string | Node3D
  type: 'prerequisite' | 'related' | 'contains' | 'similar'
  weight: number
  strength: number
  color?: string
  width?: number
}

interface KnowledgeGraph3DData {
  nodes: Node3D[]
  links: Link3D[]
}

interface KnowledgeGraph3DProps {
  data: KnowledgeGraph3DData
  width?: number
  height?: number
  onNodeClick?: (node: Node3D) => void
  onLinkClick?: (link: Link3D) => void
  onNodeHover?: (node: Node3D | null) => void
  backgroundColor?: string
  showLabels?: boolean
  enableVR?: boolean
}

const nodeTypeColors = {
  knowledge_point: '#3b82f6',
  question: '#10b981',
  concept: '#f59e0b'
}

const linkTypeColors = {
  prerequisite: '#ef4444',
  related: '#8b5cf6',
  contains: '#06b6d4',
  similar: '#84cc16'
}

export function KnowledgeGraph3D({
  data,
  width = 800,
  height = 600,
  onNodeClick,
  onLinkClick,
  onNodeHover,
  backgroundColor = '#000011',
  showLabels = true,
  enableVR = false
}: KnowledgeGraph3DProps) {
  const fgRef = useRef<any>()
  const [selectedNode, setSelectedNode] = useState<Node3D | null>(null)
  const [isPlaying, setIsPlaying] = useState(true)
  const [showParticles, setShowParticles] = useState(true)
  const [nodeSize, setNodeSize] = useState([5])
  const [linkWidth, setLinkWidth] = useState([2])
  const [linkDistance, setLinkDistance] = useState([100])
  const [chargeStrength, setChargeStrength] = useState([300])
  const [showNodeLabels, setShowNodeLabels] = useState(showLabels)
  const [showLinkLabels, setShowLinkLabels] = useState(false)

  // 处理数据，添加颜色和大小
  const processedData = useCallback(() => {
    const processedNodes = data.nodes.map(node => ({
      ...node,
      color: nodeTypeColors[node.type],
      size: Math.sqrt(node.weight) * nodeSize[0] + 5
    }))

    const processedLinks = data.links.map(link => ({
      ...link,
      color: linkTypeColors[link.type],
      width: Math.sqrt(link.weight) * linkWidth[0]
    }))

    return {
      nodes: processedNodes,
      links: processedLinks
    }
  }, [data, nodeSize, linkWidth])

  const handleNodeClick = useCallback((node: Node3D) => {
    setSelectedNode(node)
    onNodeClick?.(node)
    
    // 聚焦到节点
    if (fgRef.current) {
      const distance = 200
      const distRatio = 1 + distance / Math.hypot(node.x || 0, node.y || 0, node.z || 0)
      
      fgRef.current.cameraPosition(
        { 
          x: (node.x || 0) * distRatio, 
          y: (node.y || 0) * distRatio, 
          z: (node.z || 0) * distRatio 
        },
        node,
        3000
      )
    }
  }, [onNodeClick])

  const handleNodeHover = useCallback((node: Node3D | null) => {
    onNodeHover?.(node)
    
    // 高亮相关节点
    if (fgRef.current) {
      fgRef.current.nodeColor(fgRef.current.nodeColor())
      fgRef.current.linkColor(fgRef.current.linkColor())
      
      if (node) {
        const highlightNodes = new Set([node.id])
        const highlightLinks = new Set()
        
        data.links.forEach(link => {
          if (link.source === node.id || link.target === node.id) {
            highlightLinks.add(link)
            highlightNodes.add(typeof link.source === 'string' ? link.source : link.source.id)
            highlightNodes.add(typeof link.target === 'string' ? link.target : link.target.id)
          }
        })
        
        fgRef.current.nodeColor((n: Node3D) => 
          highlightNodes.has(n.id) ? n.color : '#666666'
        )
        fgRef.current.linkColor((l: Link3D) => 
          highlightLinks.has(l) ? l.color : '#333333'
        )
      }
    }
  }, [onNodeHover, data.links])

  const handleReset = () => {
    if (fgRef.current) {
      fgRef.current.cameraPosition({ x: 0, y: 0, z: 400 }, { x: 0, y: 0, z: 0 }, 2000)
      fgRef.current.refresh()
    }
  }

  const handlePauseResume = () => {
    if (fgRef.current) {
      if (isPlaying) {
        fgRef.current.pauseAnimation()
      } else {
        fgRef.current.resumeAnimation()
      }
      setIsPlaying(!isPlaying)
    }
  }

  const handleExport = () => {
    if (fgRef.current) {
      const canvas = fgRef.current.renderer().domElement
      const link = document.createElement('a')
      link.download = 'knowledge-graph-3d.png'
      link.href = canvas.toDataURL()
      link.click()
    }
  }

  const nodeLabel = useCallback((node: Node3D) => {
    return showNodeLabels ? node.name : ''
  }, [showNodeLabels])

  const linkLabel = useCallback((link: Link3D) => {
    return showLinkLabels ? link.type : ''
  }, [showLinkLabels])

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>3D知识图谱</CardTitle>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={handleReset}>
              <RotateCcw className="w-4 h-4" />
            </Button>
            <Button variant="outline" size="sm" onClick={handlePauseResume}>
              {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
            </Button>
            <Button variant="outline" size="sm" onClick={handleExport}>
              <Download className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* 控制面板 */}
          <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium w-20">节点大小:</span>
                <Slider
                  value={nodeSize}
                  onValueChange={setNodeSize}
                  max={20}
                  min={1}
                  step={1}
                  className="flex-1"
                />
                <span className="text-sm text-gray-600 w-8">{nodeSize[0]}</span>
              </div>
              
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium w-20">连接宽度:</span>
                <Slider
                  value={linkWidth}
                  onValueChange={setLinkWidth}
                  max={10}
                  min={0.5}
                  step={0.5}
                  className="flex-1"
                />
                <span className="text-sm text-gray-600 w-8">{linkWidth[0]}</span>
              </div>
              
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium w-20">连接距离:</span>
                <Slider
                  value={linkDistance}
                  onValueChange={setLinkDistance}
                  max={300}
                  min={50}
                  step={10}
                  className="flex-1"
                />
                <span className="text-sm text-gray-600 w-8">{linkDistance[0]}</span>
              </div>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium w-20">排斥力:</span>
                <Slider
                  value={chargeStrength}
                  onValueChange={setChargeStrength}
                  max={1000}
                  min={100}
                  step={50}
                  className="flex-1"
                />
                <span className="text-sm text-gray-600 w-8">{chargeStrength[0]}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">节点标签</span>
                <Switch 
                  checked={showNodeLabels} 
                  onCheckedChange={setShowNodeLabels}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">连接标签</span>
                <Switch 
                  checked={showLinkLabels} 
                  onCheckedChange={setShowLinkLabels}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">粒子效果</span>
                <Switch 
                  checked={showParticles} 
                  onCheckedChange={setShowParticles}
                />
              </div>
            </div>
          </div>

          {/* 选中节点信息 */}
          {selectedNode && (
            <div className="p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium mb-2">选中节点: {selectedNode.name}</h4>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div>类型: <Badge variant="outline">{selectedNode.type}</Badge></div>
                <div>层级: {selectedNode.level}</div>
                <div>权重: {selectedNode.weight}</div>
                <div>分组: {selectedNode.group}</div>
              </div>
              {selectedNode.description && (
                <p className="text-sm text-gray-600 mt-2">{selectedNode.description}</p>
              )}
            </div>
          )}

          {/* 3D图形 */}
          <div className="border rounded-lg overflow-hidden">
            <ForceGraph3D
              ref={fgRef}
              graphData={processedData()}
              width={width}
              height={height}
              backgroundColor={backgroundColor}
              nodeLabel={nodeLabel}
              nodeColor="color"
              nodeVal="size"
              linkLabel={linkLabel}
              linkColor="color"
              linkWidth="width"
              linkDirectionalArrowLength={3.5}
              linkDirectionalArrowRelPos={1}
              linkDirectionalParticles={showParticles ? 2 : 0}
              linkDirectionalParticleSpeed={0.006}
              linkDirectionalParticleWidth={2}
              onNodeClick={handleNodeClick}
              onNodeHover={handleNodeHover}
              onLinkClick={onLinkClick}
              d3Force={{
                link: { distance: linkDistance[0] },
                charge: { strength: -chargeStrength[0] }
              }}
              enableVrMode={enableVR}
              controlType="orbit"
              showNavInfo={false}
            />
          </div>

          {/* 图例 */}
          <div className="flex items-center gap-6 p-4 bg-gray-50 rounded-lg">
            <div>
              <h4 className="text-sm font-medium mb-2">节点类型</h4>
              <div className="flex gap-3">
                {Object.entries(nodeTypeColors).map(([type, color]) => (
                  <div key={type} className="flex items-center gap-1">
                    <div 
                      className="w-3 h-3 rounded-full" 
                      style={{ backgroundColor: color }}
                    />
                    <span className="text-xs">{type}</span>
                  </div>
                ))}
              </div>
            </div>
            <div>
              <h4 className="text-sm font-medium mb-2">连接类型</h4>
              <div className="flex gap-3">
                {Object.entries(linkTypeColors).map(([type, color]) => (
                  <div key={type} className="flex items-center gap-1">
                    <div 
                      className="w-4 h-0.5" 
                      style={{ backgroundColor: color }}
                    />
                    <span className="text-xs">{type}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* 操作提示 */}
          <div className="text-xs text-gray-500 p-2 bg-gray-50 rounded">
            <p>• 鼠标左键拖拽旋转视角</p>
            <p>• 鼠标滚轮缩放</p>
            <p>• 点击节点聚焦</p>
            <p>• 悬停节点高亮相关连接</p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
