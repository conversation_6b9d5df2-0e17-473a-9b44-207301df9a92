/**
 * 标注任务列表组件
 */

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Search, Plus, Edit, Trash2, Eye, Play, Pause, CheckCircle, UserCheck, Clock, XCircle } from 'lucide-react'

interface AnnotationTask {
  taskId: number
  title: string
  description?: string
  taskType: string
  priority: number
  status: number
  progress: number
  deadline?: string
  totalQuestions: number
  completedQuestions: number
  createdBy: number
  reviewerId?: number
  createdAt: string
  updatedAt: string
  reviewedAt?: string
}

interface AnnotationTaskListProps {
  onCreateNew?: () => void
  onEdit?: (task: AnnotationTask) => void
  onView?: (task: AnnotationTask) => void
  onDelete?: (task: AnnotationTask) => void
  onStart?: (task: AnnotationTask) => void
  onSubmitForReview?: (task: AnnotationTask) => void
  onAssignReviewer?: (task: AnnotationTask) => void
  onReview?: (task: AnnotationTask) => void
}

export function AnnotationTaskList({
  onCreateNew,
  onEdit,
  onView,
  onDelete,
  onStart,
  onSubmitForReview,
  onAssignReviewer,
  onReview
}: AnnotationTaskListProps) {
  const [tasks, setTasks] = useState<AnnotationTask[]>([])
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(20)
  const [searchTitle, setSearchTitle] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')

  // 模拟数据 - 实际应该从API获取
  useEffect(() => {
    loadTasks()
  }, [currentPage, searchTitle, statusFilter])

  const loadTasks = async () => {
    try {
      setLoading(true)
      // 这里应该调用实际的API
      // const response = await AnnotationTaskService.getTasks(...)
      
      // 模拟数据
      const mockTasks: AnnotationTask[] = [
        {
          taskId: 1,
          title: '数学题目标注任务',
          description: '对高中数学题目进行知识点标注',
          taskType: 'knowledge_annotation',
          priority: 3,
          status: 1, // 进行中
          progress: 65,
          deadline: '2024-01-15',
          totalQuestions: 100,
          completedQuestions: 65,
          createdBy: 1,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-10T00:00:00Z'
        },
        {
          taskId: 2,
          title: '物理题目标注任务',
          description: '对高中物理题目进行知识点标注',
          taskType: 'knowledge_annotation',
          priority: 2,
          status: 0, // 待开始
          progress: 0,
          deadline: '2024-01-20',
          totalQuestions: 80,
          completedQuestions: 0,
          createdBy: 1,
          createdAt: '2024-01-05T00:00:00Z',
          updatedAt: '2024-01-05T00:00:00Z'
        },
        {
          taskId: 3,
          title: '化学题目标注任务',
          description: '对高中化学题目进行知识点标注',
          taskType: 'knowledge_annotation',
          priority: 1,
          status: 3, // 审核中
          progress: 100,
          totalQuestions: 60,
          completedQuestions: 60,
          createdBy: 1,
          createdAt: '2023-12-20T00:00:00Z',
          updatedAt: '2024-01-08T00:00:00Z'
        }
      ]
      
      setTasks(mockTasks)
      setTotal(mockTasks.length)
    } catch (error) {
      console.error('加载任务列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = (status: number) => {
    const statusMap = {
      0: { label: '待开始', color: 'bg-gray-100 text-gray-800' },
      1: { label: '进行中', color: 'bg-blue-100 text-blue-800' },
      2: { label: '已完成', color: 'bg-green-100 text-green-800' },
      3: { label: '审核中', color: 'bg-yellow-100 text-yellow-800' },
      4: { label: '已审核', color: 'bg-purple-100 text-purple-800' },
      5: { label: '已拒绝', color: 'bg-red-100 text-red-800' },
      6: { label: '已取消', color: 'bg-gray-100 text-gray-800' }
    }
    
    const statusInfo = statusMap[status as keyof typeof statusMap] || statusMap[0]
    
    return (
      <Badge className={statusInfo.color}>
        {statusInfo.label}
      </Badge>
    )
  }

  const getPriorityBadge = (priority: number) => {
    const priorityMap = {
      1: { label: '低', color: 'bg-green-100 text-green-800' },
      2: { label: '中', color: 'bg-yellow-100 text-yellow-800' },
      3: { label: '高', color: 'bg-orange-100 text-orange-800' },
      4: { label: '紧急', color: 'bg-red-100 text-red-800' },
      5: { label: '最高', color: 'bg-purple-100 text-purple-800' }
    }
    
    const priorityInfo = priorityMap[priority as keyof typeof priorityMap] || priorityMap[1]
    
    return (
      <Badge className={priorityInfo.color}>
        {priorityInfo.label}
      </Badge>
    )
  }

  const getStatusIcon = (status: number) => {
    switch (status) {
      case 0:
        return <Play className="w-4 h-4" />
      case 1:
        return <Pause className="w-4 h-4" />
      case 2:
        return <CheckCircle className="w-4 h-4" />
      case 3:
        return <Clock className="w-4 h-4" />
      case 4:
        return <UserCheck className="w-4 h-4" />
      case 5:
        return <XCircle className="w-4 h-4" />
      default:
        return null
    }
  }

  const isOverdue = (deadline?: string) => {
    if (!deadline) return false
    return new Date(deadline) < new Date()
  }

  const totalPages = Math.ceil(total / pageSize)

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>标注任务管理</CardTitle>
          <Button onClick={onCreateNew}>
            <Plus className="w-4 h-4 mr-2" />
            新建任务
          </Button>
        </div>
        
        {/* 搜索和筛选 */}
        <div className="flex gap-4 mt-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="搜索任务标题..."
                className="pl-10"
                value={searchTitle}
                onChange={(e) => setSearchTitle(e.target.value)}
              />
            </div>
          </div>
          
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="任务状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">全部状态</SelectItem>
              <SelectItem value="0">待开始</SelectItem>
              <SelectItem value="1">进行中</SelectItem>
              <SelectItem value="2">已完成</SelectItem>
              <SelectItem value="3">审核中</SelectItem>
              <SelectItem value="4">已审核</SelectItem>
              <SelectItem value="5">已拒绝</SelectItem>
              <SelectItem value="6">已取消</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      
      <CardContent>
        {loading ? (
          <div className="text-center py-8">加载中...</div>
        ) : (
          <>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>任务信息</TableHead>
                  <TableHead>优先级</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>进度</TableHead>
                  <TableHead>截止时间</TableHead>
                  <TableHead>创建时间</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {tasks.map((task) => (
                  <TableRow key={task.taskId}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{task.title}</div>
                        {task.description && (
                          <div className="text-sm text-gray-500 truncate max-w-xs">
                            {task.description}
                          </div>
                        )}
                        <div className="text-xs text-gray-400 mt-1">
                          {task.completedQuestions}/{task.totalQuestions} 题目
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {getPriorityBadge(task.priority)}
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(task.status)}
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <Progress value={task.progress} className="h-2" />
                        <div className="text-xs text-gray-500">
                          {task.progress}%
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {task.deadline ? (
                        <div className={isOverdue(task.deadline) ? 'text-red-600' : ''}>
                          {new Date(task.deadline).toLocaleDateString()}
                          {isOverdue(task.deadline) && (
                            <div className="text-xs text-red-500">已逾期</div>
                          )}
                        </div>
                      ) : (
                        <span className="text-gray-400">无</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {new Date(task.createdAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        {task.status === 0 && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onStart?.(task)}
                            title="开始任务"
                          >
                            <Play className="w-4 h-4" />
                          </Button>
                        )}
                        {task.status === 2 && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onSubmitForReview?.(task)}
                            title="提交审核"
                          >
                            <Clock className="w-4 h-4" />
                          </Button>
                        )}
                        {task.status === 3 && (
                          <>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => onAssignReviewer?.(task)}
                              title="分配审核员"
                            >
                              <UserCheck className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => onReview?.(task)}
                              title="开始审核"
                            >
                              <CheckCircle className="w-4 h-4" />
                            </Button>
                          </>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onView?.(task)}
                          title="查看详情"
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        {task.status < 3 && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onEdit?.(task)}
                            title="编辑任务"
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                        )}
                        {task.status < 2 && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onDelete?.(task)}
                            title="删除任务"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            
            {tasks.length === 0 && !loading && (
              <div className="text-center py-8 text-gray-500">
                <div>暂无标注任务</div>
                <Button className="mt-4" onClick={onCreateNew}>
                  <Plus className="w-4 h-4 mr-2" />
                  创建第一个任务
                </Button>
              </div>
            )}
            
            {/* 分页 */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between mt-4">
                <div className="text-sm text-gray-500">
                  共 {total} 条记录，第 {currentPage} / {totalPages} 页
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={currentPage === 1}
                    onClick={() => setCurrentPage(prev => prev - 1)}
                  >
                    上一页
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={currentPage === totalPages}
                    onClick={() => setCurrentPage(prev => prev + 1)}
                  >
                    下一页
                  </Button>
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  )
}
