/**
 * 可视化工具函数
 */

import * as d3 from 'd3'

// 颜色主题
export const colorSchemes = {
  default: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'],
  pastel: ['#fecaca', '#fed7d7', '#fef3c7', '#d1fae5', '#dbeafe', '#e0e7ff'],
  dark: ['#1e40af', '#059669', '#d97706', '#dc2626', '#7c3aed', '#0891b2'],
  categorical: d3.schemeCategory10,
  sequential: d3.schemeBlues[9],
  diverging: d3.schemeRdYlBu[11]
}

// 数据转换工具
export class DataTransformer {
  /**
   * 将平面数据转换为层次结构
   */
  static toHierarchy(data: any[], idField = 'id', parentField = 'parentId', childrenField = 'children') {
    const map = new Map()
    const roots: any[] = []

    // 创建节点映射
    data.forEach(item => {
      map.set(item[idField], { ...item, [childrenField]: [] })
    })

    // 构建层次结构
    data.forEach(item => {
      const node = map.get(item[idField])
      const parentId = item[parentField]
      
      if (parentId && map.has(parentId)) {
        map.get(parentId)[childrenField].push(node)
      } else {
        roots.push(node)
      }
    })

    return roots
  }

  /**
   * 将网络数据转换为D3力导向图格式
   */
  static toForceGraph(nodes: any[], links: any[]) {
    const nodeMap = new Map()
    
    // 处理节点
    const processedNodes = nodes.map((node, index) => ({
      ...node,
      index,
      x: Math.random() * 800,
      y: Math.random() * 600
    }))

    processedNodes.forEach(node => {
      nodeMap.set(node.id, node)
    })

    // 处理连接
    const processedLinks = links.map(link => ({
      ...link,
      source: nodeMap.get(link.source) || link.source,
      target: nodeMap.get(link.target) || link.target
    }))

    return { nodes: processedNodes, links: processedLinks }
  }

  /**
   * 数据聚合
   */
  static aggregate(data: any[], groupBy: string, aggregateField: string, aggregateFunc = 'sum') {
    const groups = d3.group(data, d => d[groupBy])
    const result: any[] = []

    groups.forEach((values, key) => {
      let aggregatedValue: number

      switch (aggregateFunc) {
        case 'sum':
          aggregatedValue = d3.sum(values, d => d[aggregateField])
          break
        case 'mean':
          aggregatedValue = d3.mean(values, d => d[aggregateField]) || 0
          break
        case 'max':
          aggregatedValue = d3.max(values, d => d[aggregateField]) || 0
          break
        case 'min':
          aggregatedValue = d3.min(values, d => d[aggregateField]) || 0
          break
        case 'count':
          aggregatedValue = values.length
          break
        default:
          aggregatedValue = d3.sum(values, d => d[aggregateField])
      }

      result.push({
        [groupBy]: key,
        [aggregateField]: aggregatedValue,
        count: values.length,
        items: values
      })
    })

    return result
  }

  /**
   * 时间序列数据处理
   */
  static processTimeSeries(data: any[], timeField: string, valueField: string, interval = 'day') {
    const timeFormat = interval === 'day' ? d3.timeDay : 
                      interval === 'week' ? d3.timeWeek :
                      interval === 'month' ? d3.timeMonth : d3.timeDay

    const parseTime = d3.timeParse('%Y-%m-%d')
    const formatTime = d3.timeFormat('%Y-%m-%d')

    // 解析时间并按间隔分组
    const processedData = data.map(d => ({
      ...d,
      parsedTime: parseTime(d[timeField]) || new Date(d[timeField])
    }))

    const grouped = d3.group(processedData, d => 
      formatTime(timeFormat.floor(d.parsedTime))
    )

    const result: any[] = []
    grouped.forEach((values, key) => {
      result.push({
        time: key,
        value: d3.sum(values, d => d[valueField]),
        count: values.length,
        items: values
      })
    })

    return result.sort((a, b) => new Date(a.time).getTime() - new Date(b.time).getTime())
  }
}

// 布局算法
export class LayoutAlgorithms {
  /**
   * 力导向布局
   */
  static forceDirected(nodes: any[], links: any[], options = {}) {
    const {
      width = 800,
      height = 600,
      linkDistance = 100,
      chargeStrength = -300,
      iterations = 300
    } = options

    const simulation = d3.forceSimulation(nodes)
      .force('link', d3.forceLink(links).id((d: any) => d.id).distance(linkDistance))
      .force('charge', d3.forceManyBody().strength(chargeStrength))
      .force('center', d3.forceCenter(width / 2, height / 2))
      .force('collision', d3.forceCollide().radius(d => (d as any).size || 5))

    // 运行指定次数的迭代
    for (let i = 0; i < iterations; i++) {
      simulation.tick()
    }

    simulation.stop()
    return { nodes, links }
  }

  /**
   * 层次布局
   */
  static hierarchical(data: any[], options = {}) {
    const {
      width = 800,
      height = 600,
      nodeSize = [100, 50]
    } = options

    const root = d3.hierarchy(data)
    const treeLayout = d3.tree().nodeSize(nodeSize)
    
    treeLayout(root)

    // 调整坐标到画布中心
    const nodes = root.descendants()
    const links = root.links()

    const xExtent = d3.extent(nodes, d => d.x) as [number, number]
    const yExtent = d3.extent(nodes, d => d.y) as [number, number]

    const xOffset = width / 2 - (xExtent[0] + xExtent[1]) / 2
    const yOffset = height / 2 - (yExtent[0] + yExtent[1]) / 2

    nodes.forEach(node => {
      node.x += xOffset
      node.y += yOffset
    })

    return { nodes, links }
  }

  /**
   * 圆形布局
   */
  static circular(nodes: any[], options = {}) {
    const {
      width = 800,
      height = 600,
      radius = Math.min(width, height) / 2 - 50
    } = options

    const centerX = width / 2
    const centerY = height / 2
    const angleStep = (2 * Math.PI) / nodes.length

    nodes.forEach((node, index) => {
      const angle = index * angleStep
      node.x = centerX + radius * Math.cos(angle)
      node.y = centerY + radius * Math.sin(angle)
    })

    return nodes
  }

  /**
   * 网格布局
   */
  static grid(nodes: any[], options = {}) {
    const {
      width = 800,
      height = 600,
      columns = Math.ceil(Math.sqrt(nodes.length))
    } = options

    const rows = Math.ceil(nodes.length / columns)
    const cellWidth = width / columns
    const cellHeight = height / rows

    nodes.forEach((node, index) => {
      const col = index % columns
      const row = Math.floor(index / columns)
      
      node.x = col * cellWidth + cellWidth / 2
      node.y = row * cellHeight + cellHeight / 2
    })

    return nodes
  }
}

// 动画工具
export class AnimationUtils {
  /**
   * 创建数值插值动画
   */
  static interpolateNumber(from: number, to: number, duration: number, callback: (value: number) => void) {
    const interpolator = d3.interpolateNumber(from, to)
    const startTime = Date.now()

    function animate() {
      const elapsed = Date.now() - startTime
      const progress = Math.min(elapsed / duration, 1)
      const easedProgress = d3.easeQuadInOut(progress)
      
      callback(interpolator(easedProgress))

      if (progress < 1) {
        requestAnimationFrame(animate)
      }
    }

    requestAnimationFrame(animate)
  }

  /**
   * 创建颜色插值动画
   */
  static interpolateColor(from: string, to: string, duration: number, callback: (color: string) => void) {
    const interpolator = d3.interpolateRgb(from, to)
    const startTime = Date.now()

    function animate() {
      const elapsed = Date.now() - startTime
      const progress = Math.min(elapsed / duration, 1)
      const easedProgress = d3.easeQuadInOut(progress)
      
      callback(interpolator(easedProgress))

      if (progress < 1) {
        requestAnimationFrame(animate)
      }
    }

    requestAnimationFrame(animate)
  }

  /**
   * 创建路径动画
   */
  static animatePath(path: string, duration: number, callback: (pathSegment: string) => void) {
    const pathNode = document.createElementNS('http://www.w3.org/2000/svg', 'path')
    pathNode.setAttribute('d', path)
    const totalLength = pathNode.getTotalLength()

    const startTime = Date.now()

    function animate() {
      const elapsed = Date.now() - startTime
      const progress = Math.min(elapsed / duration, 1)
      const easedProgress = d3.easeQuadInOut(progress)
      
      const currentLength = totalLength * easedProgress
      const point = pathNode.getPointAtLength(currentLength)
      
      callback(`M0,0 L${point.x},${point.y}`)

      if (progress < 1) {
        requestAnimationFrame(animate)
      }
    }

    requestAnimationFrame(animate)
  }
}

// 导出工具
export class ExportUtils {
  /**
   * 导出SVG为PNG
   */
  static svgToPng(svgElement: SVGSVGElement, filename = 'chart.png') {
    const svgData = new XMLSerializer().serializeToString(svgElement)
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()

    const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' })
    const url = URL.createObjectURL(svgBlob)

    img.onload = () => {
      canvas.width = img.width
      canvas.height = img.height
      ctx?.drawImage(img, 0, 0)
      
      canvas.toBlob(blob => {
        if (blob) {
          const link = document.createElement('a')
          link.download = filename
          link.href = URL.createObjectURL(blob)
          link.click()
        }
      })
      
      URL.revokeObjectURL(url)
    }

    img.src = url
  }

  /**
   * 导出数据为CSV
   */
  static dataToCSV(data: any[], filename = 'data.csv') {
    if (!data.length) return

    const headers = Object.keys(data[0])
    const csvContent = [
      headers.join(','),
      ...data.map(row => 
        headers.map(header => {
          const value = row[header]
          return typeof value === 'string' && value.includes(',') 
            ? `"${value}"` 
            : value
        }).join(',')
      )
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    link.download = filename
    link.href = URL.createObjectURL(blob)
    link.click()
  }

  /**
   * 导出数据为JSON
   */
  static dataToJSON(data: any, filename = 'data.json') {
    const jsonContent = JSON.stringify(data, null, 2)
    const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' })
    const link = document.createElement('a')
    link.download = filename
    link.href = URL.createObjectURL(blob)
    link.click()
  }
}
