/**
 * 高级可视化页面
 */

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  BarChart3, 
  Network, 
  Zap, 
  Eye,
  Settings,
  Download,
  RefreshCw,
  Maximize,
  Share
} from 'lucide-react'
import { toast } from 'sonner'

import { KnowledgeGraph } from '@/components/visualization/knowledge-graph'
import { KnowledgeGraph3D } from '@/components/visualization/knowledge-graph-3d'
import { AdvancedAnalyticsDashboard } from '@/components/visualization/advanced-analytics-dashboard'
import { InteractiveNetwork } from '@/components/visualization/interactive-network'
import { AnimatedCharts } from '@/components/visualization/animated-charts'

interface VisualizationData {
  knowledgeGraph: {
    nodes: any[]
    links: any[]
  }
  analytics: any
  network: {
    nodes: any[]
    edges: any[]
  }
  animatedData: any[][]
}

export function AdvancedVisualizationPage() {
  const [activeTab, setActiveTab] = useState('knowledge-graph')
  const [data, setData] = useState<VisualizationData | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedDataset, setSelectedDataset] = useState('mathematics')
  const [viewMode, setViewMode] = useState('2d')

  useEffect(() => {
    loadVisualizationData()
  }, [selectedDataset])

  const loadVisualizationData = async () => {
    setLoading(true)
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 生成模拟数据
      const mockData: VisualizationData = {
        knowledgeGraph: {
          nodes: [
            {
              id: '1',
              name: '线性代数',
              type: 'knowledge_point',
              level: 1,
              weight: 10,
              group: 'mathematics',
              description: '线性代数基础概念'
            },
            {
              id: '2',
              name: '矩阵运算',
              type: 'knowledge_point',
              level: 2,
              weight: 8,
              group: 'mathematics',
              description: '矩阵的基本运算'
            },
            {
              id: '3',
              name: '向量空间',
              type: 'concept',
              level: 2,
              weight: 6,
              group: 'mathematics',
              description: '向量空间的概念'
            },
            {
              id: '4',
              name: '题目1',
              type: 'question',
              level: 3,
              weight: 4,
              group: 'practice',
              description: '线性代数练习题'
            },
            {
              id: '5',
              name: '题目2',
              type: 'question',
              level: 3,
              weight: 5,
              group: 'practice',
              description: '矩阵运算练习题'
            }
          ],
          links: [
            {
              source: '1',
              target: '2',
              type: 'prerequisite',
              weight: 0.8,
              strength: 0.7
            },
            {
              source: '1',
              target: '3',
              type: 'related',
              weight: 0.6,
              strength: 0.5
            },
            {
              source: '2',
              target: '4',
              type: 'contains',
              weight: 0.9,
              strength: 0.8
            },
            {
              source: '2',
              target: '5',
              type: 'contains',
              weight: 0.7,
              strength: 0.6
            },
            {
              source: '3',
              target: '4',
              type: 'related',
              weight: 0.5,
              strength: 0.4
            }
          ]
        },
        analytics: {
          overview: {
            totalQuestions: 1234,
            totalKnowledgePoints: 567,
            totalMappings: 2890,
            annotationCoverage: 85,
            qualityScore: 8.7,
            activeUsers: 23
          },
          trends: [
            { date: '2024-01-01', questions: 100, mappings: 250, quality: 85, users: 15 },
            { date: '2024-01-02', questions: 120, mappings: 280, quality: 87, users: 18 },
            { date: '2024-01-03', questions: 110, mappings: 260, quality: 86, users: 16 },
            { date: '2024-01-04', questions: 140, mappings: 320, quality: 89, users: 20 },
            { date: '2024-01-05', questions: 130, mappings: 300, quality: 88, users: 19 }
          ],
          distribution: [
            { name: '优秀', value: 45, color: '#00C49F' },
            { name: '良好', value: 30, color: '#0088FE' },
            { name: '一般', value: 20, color: '#FFBB28' },
            { name: '待改进', value: 5, color: '#FF8042' }
          ],
          performance: [
            { subject: '准确性', accuracy: 92, consistency: 88, completeness: 95, efficiency: 85 },
            { subject: '一致性', accuracy: 88, consistency: 92, completeness: 90, efficiency: 87 },
            { subject: '完整性', accuracy: 95, consistency: 90, completeness: 98, efficiency: 82 },
            { subject: '效率', accuracy: 85, consistency: 87, completeness: 82, efficiency: 95 }
          ],
          heatmap: Array.from({ length: 100 }, (_, i) => ({
            x: `${i % 10}`,
            y: `${Math.floor(i / 10)}`,
            value: Math.random() * 100
          })),
          funnel: [
            { name: '原始数据', value: 1000, fill: '#8884d8' },
            { name: '预处理', value: 800, fill: '#83a6ed' },
            { name: '标注完成', value: 600, fill: '#8dd1e1' },
            { name: '质量检查', value: 500, fill: '#82ca9d' },
            { name: '最终发布', value: 400, fill: '#a4de6c' }
          ],
          network: {
            nodes: [],
            links: []
          }
        },
        network: {
          nodes: [
            {
              id: '1',
              label: '线性代数',
              group: 'knowledge_point',
              level: 1,
              size: 20
            },
            {
              id: '2',
              label: '矩阵运算',
              group: 'knowledge_point',
              level: 2,
              size: 15
            },
            {
              id: '3',
              label: '向量空间',
              group: 'concept',
              level: 2,
              size: 12
            }
          ],
          edges: [
            {
              id: 'e1',
              from: '1',
              to: '2',
              weight: 0.8,
              arrows: 'to'
            },
            {
              id: 'e2',
              from: '1',
              to: '3',
              weight: 0.6,
              arrows: 'to'
            }
          ]
        },
        animatedData: [
          [
            { id: '1', name: 'A', value: 10, color: '#8884d8', timestamp: 1 },
            { id: '2', name: 'B', value: 20, color: '#82ca9d', timestamp: 1 },
            { id: '3', name: 'C', value: 15, color: '#ffc658', timestamp: 1 }
          ],
          [
            { id: '1', name: 'A', value: 15, color: '#8884d8', timestamp: 2 },
            { id: '2', name: 'B', value: 25, color: '#82ca9d', timestamp: 2 },
            { id: '3', name: 'C', value: 18, color: '#ffc658', timestamp: 2 }
          ],
          [
            { id: '1', name: 'A', value: 12, color: '#8884d8', timestamp: 3 },
            { id: '2', name: 'B', value: 30, color: '#82ca9d', timestamp: 3 },
            { id: '3', name: 'C', value: 22, color: '#ffc658', timestamp: 3 }
          ]
        ]
      }
      
      setData(mockData)
    } catch (error) {
      toast.error('加载可视化数据失败')
    } finally {
      setLoading(false)
    }
  }

  const handleExport = (format: string) => {
    toast.success(`导出${format}格式成功`)
  }

  const handleShare = () => {
    toast.success('分享链接已复制到剪贴板')
  }

  const handleNodeClick = (node: any) => {
    toast.info(`点击节点: ${node.name || node.label}`)
  }

  const handleDatasetChange = (dataset: string) => {
    setSelectedDataset(dataset)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">加载可视化数据中...</span>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">暂无可视化数据</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">高级可视化</h1>
          <p className="text-gray-600">知识图谱、数据分析和交互式可视化</p>
        </div>
        <div className="flex items-center gap-3">
          <Select value={selectedDataset} onValueChange={handleDatasetChange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="mathematics">数学</SelectItem>
              <SelectItem value="physics">物理</SelectItem>
              <SelectItem value="chemistry">化学</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={() => loadVisualizationData()}>
            <RefreshCw className="w-4 h-4 mr-2" />
            刷新
          </Button>
          <Button variant="outline" onClick={handleShare}>
            <Share className="w-4 h-4 mr-2" />
            分享
          </Button>
          <Button variant="outline" onClick={() => handleExport('png')}>
            <Download className="w-4 h-4 mr-2" />
            导出
          </Button>
        </div>
      </div>

      {/* 统计概览 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">节点数量</p>
                <p className="text-2xl font-bold">{data.knowledgeGraph.nodes.length}</p>
              </div>
              <Network className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">连接数量</p>
                <p className="text-2xl font-bold">{data.knowledgeGraph.links.length}</p>
              </div>
              <Zap className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">可视化类型</p>
                <p className="text-2xl font-bold">5</p>
              </div>
              <Eye className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">数据集</p>
                <p className="text-2xl font-bold">
                  <Badge variant="outline">{selectedDataset}</Badge>
                </p>
              </div>
              <BarChart3 className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 可视化内容 */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="knowledge-graph">知识图谱</TabsTrigger>
          <TabsTrigger value="analytics">数据分析</TabsTrigger>
          <TabsTrigger value="network">网络图</TabsTrigger>
          <TabsTrigger value="animated">动态图表</TabsTrigger>
          <TabsTrigger value="3d">3D可视化</TabsTrigger>
        </TabsList>

        <TabsContent value="knowledge-graph" className="space-y-4">
          <div className="flex items-center gap-2 mb-4">
            <Button
              variant={viewMode === '2d' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('2d')}
            >
              2D视图
            </Button>
            <Button
              variant={viewMode === '3d' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('3d')}
            >
              3D视图
            </Button>
          </div>
          
          {viewMode === '2d' ? (
            <KnowledgeGraph
              data={data.knowledgeGraph}
              onNodeClick={handleNodeClick}
              width={1000}
              height={600}
            />
          ) : (
            <KnowledgeGraph3D
              data={data.knowledgeGraph}
              onNodeClick={handleNodeClick}
              width={1000}
              height={600}
            />
          )}
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <AdvancedAnalyticsDashboard
            data={data.analytics}
            onExport={handleExport}
            refreshData={loadVisualizationData}
          />
        </TabsContent>

        <TabsContent value="network" className="space-y-4">
          <InteractiveNetwork
            nodes={data.network.nodes}
            edges={data.network.edges}
            onNodeClick={handleNodeClick}
            width={1000}
            height={600}
          />
        </TabsContent>

        <TabsContent value="animated" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <AnimatedCharts
              data={data.animatedData}
              chartType="bar"
              autoPlay={true}
              onDataChange={(currentData, frameIndex) => {
                console.log('Data changed:', currentData, frameIndex)
              }}
            />
            <AnimatedCharts
              data={data.animatedData}
              chartType="line"
              autoPlay={true}
            />
          </div>
        </TabsContent>

        <TabsContent value="3d" className="space-y-4">
          <KnowledgeGraph3D
            data={data.knowledgeGraph}
            onNodeClick={handleNodeClick}
            width={1000}
            height={600}
            enableVR={true}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}
