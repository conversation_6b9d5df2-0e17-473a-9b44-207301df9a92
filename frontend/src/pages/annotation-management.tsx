/**
 * 标注管理页面
 */

import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON>bs<PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  ClipboardList, 
  UserCheck, 
  BarChart3, 
  Settings,
  Plus,
  FileText,
  Clock,
  CheckCircle
} from 'lucide-react'
import { toast } from 'sonner'

import { AnnotationTaskList } from '@/components/annotation/annotation-task-list'
import { ReviewWorkspace } from '@/components/annotation/review-workspace'
import { TaskProgressMonitor } from '@/components/annotation/task-progress-monitor'

interface AnnotationTask {
  taskId: number
  title: string
  description?: string
  taskType: string
  priority: number
  status: number
  progress: number
  deadline?: string
  totalQuestions: number
  completedQuestions: number
  createdBy: number
  reviewerId?: number
  createdAt: string
  updatedAt: string
  reviewedAt?: string
}

export function AnnotationManagement() {
  const [activeTab, setActiveTab] = useState('tasks')
  const [selectedTask, setSelectedTask] = useState<AnnotationTask | null>(null)

  // 任务操作处理函数
  const handleCreateNewTask = () => {
    toast.info('创建新任务功能开发中...')
  }

  const handleEditTask = (task: AnnotationTask) => {
    setSelectedTask(task)
    toast.info(`编辑任务: ${task.title}`)
  }

  const handleViewTask = (task: AnnotationTask) => {
    setSelectedTask(task)
    toast.info(`查看任务: ${task.title}`)
  }

  const handleDeleteTask = (task: AnnotationTask) => {
    if (confirm(`确定要删除任务"${task.title}"吗？`)) {
      toast.success('任务删除成功')
    }
  }

  const handleStartTask = (task: AnnotationTask) => {
    toast.success(`任务"${task.title}"已开始`)
  }

  const handleSubmitForReview = (task: AnnotationTask) => {
    toast.success(`任务"${task.title}"已提交审核`)
  }

  const handleAssignReviewer = (task: AnnotationTask) => {
    toast.info('分配审核员功能开发中...')
  }

  const handleReviewTask = (task: AnnotationTask) => {
    setSelectedTask(task)
    setActiveTab('review')
    toast.info(`开始审核任务: ${task.title}`)
  }

  const handleReviewSubmit = (taskId: number, reviewData: any) => {
    toast.success('审核提交成功')
  }

  // 获取统计数据
  const getTaskStats = () => {
    // 这里应该从API获取实际数据
    return {
      total: 156,
      pending: 23,
      inProgress: 34,
      completed: 89,
      underReview: 8,
      reviewed: 2
    }
  }

  const stats = getTaskStats()

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">数据标注管理</h1>
          <p className="text-gray-600 mt-2">管理标注任务、审核流程和质量监控</p>
        </div>
        <Button onClick={handleCreateNewTask} className="flex items-center gap-2">
          <Plus className="w-4 h-4" />
          创建新任务
        </Button>
      </div>

      {/* 统计概览 */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">总任务</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">待开始</p>
                <p className="text-2xl font-bold">{stats.pending}</p>
              </div>
              <Clock className="h-8 w-8 text-gray-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">进行中</p>
                <p className="text-2xl font-bold">{stats.inProgress}</p>
              </div>
              <ClipboardList className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">已完成</p>
                <p className="text-2xl font-bold">{stats.completed}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">审核中</p>
                <p className="text-2xl font-bold">{stats.underReview}</p>
              </div>
              <UserCheck className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">已审核</p>
                <p className="text-2xl font-bold">{stats.reviewed}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 主要内容区域 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="tasks" className="flex items-center gap-2">
            <ClipboardList className="w-4 h-4" />
            任务管理
          </TabsTrigger>
          <TabsTrigger value="review" className="flex items-center gap-2">
            <UserCheck className="w-4 h-4" />
            审核工作台
          </TabsTrigger>
          <TabsTrigger value="monitor" className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            进度监控
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center gap-2">
            <Settings className="w-4 h-4" />
            系统设置
          </TabsTrigger>
        </TabsList>

        <TabsContent value="tasks" className="space-y-4">
          <AnnotationTaskList
            onCreateNew={handleCreateNewTask}
            onEdit={handleEditTask}
            onView={handleViewTask}
            onDelete={handleDeleteTask}
            onStart={handleStartTask}
            onSubmitForReview={handleSubmitForReview}
            onAssignReviewer={handleAssignReviewer}
            onReview={handleReviewTask}
          />
        </TabsContent>

        <TabsContent value="review" className="space-y-4">
          <ReviewWorkspace
            onTaskSelect={setSelectedTask}
            onReviewSubmit={handleReviewSubmit}
          />
        </TabsContent>

        <TabsContent value="monitor" className="space-y-4">
          <TaskProgressMonitor />
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>系统设置</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">工作流配置</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span>自动分配审核员</span>
                          <Badge variant="outline">已启用</Badge>
                        </div>
                        <div className="flex justify-between items-center">
                          <span>质量检查阈值</span>
                          <Badge variant="outline">85%</Badge>
                        </div>
                        <div className="flex justify-between items-center">
                          <span>超时提醒</span>
                          <Badge variant="outline">24小时</Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">通知设置</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span>任务分配通知</span>
                          <Badge variant="outline">已启用</Badge>
                        </div>
                        <div className="flex justify-between items-center">
                          <span>审核结果通知</span>
                          <Badge variant="outline">已启用</Badge>
                        </div>
                        <div className="flex justify-between items-center">
                          <span>截止时间提醒</span>
                          <Badge variant="outline">已启用</Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <div className="flex gap-4">
                  <Button variant="outline">导出配置</Button>
                  <Button variant="outline">导入配置</Button>
                  <Button>保存设置</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
